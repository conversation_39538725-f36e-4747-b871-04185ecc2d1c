{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/poppins_1bffe7f2.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"poppins_1bffe7f2-module__MT77oa__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 11, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/poppins_1bffe7f2.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.js%22,%22import%22:%22Poppins%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22display%22:%22swap%22,%22weight%22:[%22100%22,%22200%22,%22300%22,%22400%22,%22500%22,%22600%22,%22700%22,%22800%22,%22900%22]}],%22variableName%22:%22poppins%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Poppins', 'Poppins Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,uJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,uJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,uJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/common/Header.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/common/Header.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/common/Header.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/common/Header.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/common/Header.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/common/Header.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/common/Footer.jsx"], "sourcesContent": ["import React from \"react\";\nimport Image from \"next/image\";\nimport Link from \"next/link\";\nimport { ExternalLink, ArrowRight, Linkedin, Phone, Mail, MapPin } from \"lucide-react\";\nimport { FaXTwitter } from 'react-icons/fa6';\n\nconst services = [\n  { name: \"Virtual CISO (vCISO)\", href: \"/services/vciso\" },\n  { name: \"Red Teaming\", href: \"/services/red-teaming\" },\n  { name: \"AWS and Azure Configuration\", href: \"/services/aws-azure\" },\n  { name: \"Web3 Audits (Pentest)\", href: \"/services/web3-audits\" },\n  { name: \"VDP\", href: \"/services/vdp\" },\n  { name: \"VAPT\", href: \"/services/vapt\" },\n  { name: \"Bug Bounty (Through Capture The Bug)\", href: \"/services/bug-bounty\" },\n  { name: \"Compliance Pre Assessment\", href: \"/services/compliance\" },\n  { name: \"Office365 Assessment\", href: \"/services/office365\" },\n  { name: \"Cloud Assessment\", href: \"/services/cloud-assessment\" },\n  { name: \"Google WorkSpace Assessment\", href: \"/services/google-workspace\" },\n  { name: \"Incident response\", href: \"/services/incident-response\" },\n  { name: \"Source code review\", href: \"/services/source-code-review\" },\n];\n\nconst company = [\n  { name: \"About Us\", href: \"/about\" },\n  { name: \"Contact\", href: \"/contact\" },\n];\n\nconst resources = [\n  { name: \"Blogs\", href: \"/blog\" },\n  { name: \"News\", href: \"/news\" },\n];\n\nconst social = [\n  {\n    name: \"LinkedIn\",\n    href: \"#\",\n    icon: <Linkedin size={20} />,\n  },\n  {\n    name: \"Twitter\",\n    href: \"https://x.com/security_lit\",\n    icon: <FaXTwitter size={20} />,\n  },\n];\n\nconst Footer = () => {\n  return (\n    <div className=\"relative bg-transparent z-10\">\n      {/* CTA Section - Similar to CTB */}\n      <section className=\"relative z-10 w-[90%] sm:w-4/5 mx-auto overflow-hidden rounded-xl shadow-2xl transform translate-y-1/2 -mt-6 sm:-mt-16 md:-mt-24\">\n        <div className=\"bg-[var(--color-blue)] relative\">\n          <div className=\"relative py-6 sm:py-8 lg:py-16 px-6 sm:px-8 lg:px-10\">\n            <div className=\"flex flex-col sm:flex-row items-center justify-between gap-4 sm:gap-6 text-center sm:text-left\">\n              <div className=\"flex-1\">\n                <h2 className=\"text-xl sm:text-2xl lg:text-3xl font-bold text-white mb-2 sm:mb-5 leading-tight\">\n                  Enterprise-grade security that scales with you.\n                </h2>\n                <p className=\"text-sm lg:text-base text-white/80 max-w-xl mb-4 sm:mb-0\">\n                  Comprehensive cybersecurity solutions for modern businesses.\n                </p>\n              </div>\n              <div>\n                <Link\n                  href=\"/contact\"\n                  className=\"inline-flex items-center justify-center gap-2 bg-white hover:bg-white/95 text-[var(--color-blue)] font-medium px-6 py-3 sm:px-7 sm:py-3.5 rounded-lg transition-all duration-300 transform hover:-translate-y-0.5 hover:shadow-lg group relative overflow-hidden\"\n                >\n                  <span className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-[var(--color-blue)]/0 via-[var(--color-blue)]/5 to-[var(--color-blue)]/0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out\"></span>\n                  <span className=\"relative\">Get Started</span>\n                  <ArrowRight className=\"w-4 h-4 relative transition-transform group-hover:translate-x-1\" />\n                </Link>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Main Footer */}\n      <footer className=\"w-full bg-[var(--color-dark-blue)] text-white pt-32 sm:pt-28 lg:pt-32\">\n        <div className=\"max-w-screen-2xl mx-auto px-3 sm:px-4 md:px-6 lg:px-12 xl:ml-10\">\n          <div className=\"py-8 sm:py-10 border-b border-white/10\">\n            <div className=\"grid grid-cols-1 lg:grid-cols-12 gap-8 sm:gap-10 lg:gap-12\">\n\n              {/* Left Side - Brand and Contact */}\n              <div className=\"lg:col-span-4 text-center sm:text-left mx-5\">\n                <div className=\"mb-6 sm:mb-8\">\n                  <Image\n                    src=\"/seclit-logo-white.png\"\n                    alt=\"SecurityLit Logo\"\n                    width={160}\n                    height={56}\n                    className=\"h-10 sm:h-12 lg:h-14 w-auto mx-auto sm:mx-0\"\n                  />\n                </div>\n\n                <p className=\"text-white/70 text-sm leading-6 mb-6 sm:mb-8 max-w-md mx-auto sm:mx-0\">\n                  Protecting businesses from cyber threats with comprehensive, enterprise-grade security solutions tailored for modern organizations.\n                </p>\n\n                {/* Contact Information */}\n                <div className=\"mb-6 sm:mb-8\">\n                  <div className=\"space-y-4 sm:space-y-5\">\n                    <div className=\"flex items-center gap-3 text-white/70 text-sm\">\n                      <MapPin size={15} className=\"text-white/50 flex-shrink-0\" />\n                      <span>New Zealand - Hamilton, Waikato</span>\n                    </div>\n                    <div className=\"flex items-center gap-3 text-white/70 text-sm\">\n                      <MapPin size={15} className=\"text-white/50 flex-shrink-0\" />\n                      <span>India - Gurugram, Haryana</span>\n                    </div>\n                    <div className=\"flex items-center gap-3 text-white/70 text-sm\">\n                      <Mail size={15} className=\"text-white/50 flex-shrink-0\" />\n                      <a href=\"mailto:<EMAIL>\" className=\"hover:text-white transition-colors break-all sm:break-normal\"><EMAIL></a>\n                    </div>\n                    <div className=\"flex items-center gap-3 text-white/70 text-sm\">\n                      <Phone size={15} className=\"text-white/50 flex-shrink-0\" />\n                      <a href=\"tel:+6403940821\" className=\"hover:text-white transition-colors\">+64 (03) 394 0821</a>\n                    </div>\n                    <div className=\"flex items-center gap-3 text-white/70 text-sm\">\n                      <Phone size={15} className=\"text-white/50 flex-shrink-0\" />\n                      <a href=\"tel:+************\" className=\"hover:text-white transition-colors\">+91 8527 800769</a>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"flex justify-center sm:justify-start space-x-4\">\n                  {social.map((s) => (\n                    <a key={s.name} href={s.href} aria-label={s.name} className=\"text-white/70 hover:text-white transition-colors\">\n                      {s.icon}\n                    </a>\n                  ))}\n                </div>\n              </div>\n\n              {/* Right Side - Navigation Links */}\n              <div className=\"lg:col-span-8\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 sm:gap-10 lg:gap-12 justify-items-start\">\n\n                  {/* Services */}\n                  <div>\n                    <h3 className=\"text-white font-semibold text-sm mb-4 sm:mb-5\">Services</h3>\n                    <ul className=\"space-y-3 sm:space-y-4\">\n                      {services.slice(0, 8).map((item) => (\n                        <li key={item.name}>\n                          <a href={item.href} className=\"text-white/70 hover:text-white text-sm transition-colors\">{item.name}</a>\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n\n                  {/* More Services */}\n                  <div>\n                    <h3 className=\"text-white font-semibold text-sm mb-4 sm:mb-5\">More Services</h3>\n                    <ul className=\"space-y-3 sm:space-y-4\">\n                      {services.slice(8).map((item) => (\n                        <li key={item.name}>\n                          <a href={item.href} className=\"text-white/70 hover:text-white text-sm transition-colors\">{item.name}</a>\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n\n                  {/* Company */}\n                  <div>\n                    <h3 className=\"text-white font-semibold text-sm mb-4 sm:mb-5\">Company</h3>\n                    <ul className=\"space-y-3 sm:space-y-4\">\n                      {company.map((item) => (\n                        <li key={item.name}>\n                          <a href={item.href} className=\"text-white/70 hover:text-white text-sm transition-colors\">{item.name}</a>\n                        </li>\n                      ))}\n                    </ul>\n\n                    <h3 className=\"text-white font-semibold text-sm mb-4 sm:mb-5 mt-8\">Resources</h3>\n                    <ul className=\"space-y-3 sm:space-y-4\">\n                      {resources.map((item) => (\n                        <li key={item.name}>\n                          <a href={item.href} className=\"text-white/70 hover:text-white text-sm transition-colors\">{item.name}</a>\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n\n                  {/* Find Us On - Pae Hokohoko */}\n                  <div className=\"w-full lg:col-span-1 flex flex-col items-center\">\n                    <h3 className=\"text-white font-semibold text-sm mb-4 sm:mb-5 text-center w-full\">Find Us On</h3>\n                    <div className=\"bg-gradient-to-br from-white/8 to-white/3 rounded-xl p-5 border border-white/15 hover:bg-white/10 hover:border-white/20 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-xl backdrop-blur-sm w-full max-w-[280px] -translate-x-1 lg:-translate-x-2\">\n                      <div className=\"flex items-center justify-center mb-4\">\n                        <div className=\"bg-white/10 rounded-lg p-2\">\n                          <Image\n                            src=\"/images/pae-hokohoko-logo.jpeg\"\n                            alt=\"Pae Hokohoko Marketplace Logo\"\n                            width={80}\n                            height={40}\n                            className=\"object-contain rounded\"\n                          />\n                        </div>\n                      </div>\n                      <p className=\"text-xs text-white/80 text-center mb-4 leading-relaxed\">\n                        Recognized supplier of VAPT services on the Pae Hokohoko marketplace\n                      </p>\n                      <a\n                        href=\"https://marketplace.govt.nz/\"\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className=\"inline-flex items-center justify-center gap-2 w-full bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] hover:from-[var(--color-blue-secondary)] hover:to-[var(--color-blue)] text-white px-4 py-3 rounded-lg text-xs font-semibold transition-all duration-300 transform hover:-translate-y-0.5 hover:shadow-lg group\"\n                      >\n                        <span className=\"relative\">Visit Marketplace</span>\n                        <ExternalLink className=\"w-3 h-3 relative transition-transform group-hover:translate-x-1\" />\n                      </a>\n                    </div>\n                  </div>\n\n                </div>\n              </div>\n            </div>\n\n            <div className=\"mt-8 sm:mt-10 text-center\">\n              <p className=\"text-white/70 text-sm mt-4 sm:mt-5\">\n                © {new Date().getFullYear()} SecurityLit. All rights reserved. |\n                <a href=\"/privacy\" className=\"hover:text-white transition-colors ml-1\">Privacy Policy</a>\n              </p>\n            </div>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default Footer; "], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;;;;;AAEA,MAAM,WAAW;IACf;QAAE,MAAM;QAAwB,MAAM;IAAkB;IACxD;QAAE,MAAM;QAAe,MAAM;IAAwB;IACrD;QAAE,MAAM;QAA+B,MAAM;IAAsB;IACnE;QAAE,MAAM;QAAyB,MAAM;IAAwB;IAC/D;QAAE,MAAM;QAAO,MAAM;IAAgB;IACrC;QAAE,MAAM;QAAQ,MAAM;IAAiB;IACvC;QAAE,MAAM;QAAwC,MAAM;IAAuB;IAC7E;QAAE,MAAM;QAA6B,MAAM;IAAuB;IAClE;QAAE,MAAM;QAAwB,MAAM;IAAsB;IAC5D;QAAE,MAAM;QAAoB,MAAM;IAA6B;IAC/D;QAAE,MAAM;QAA+B,MAAM;IAA6B;IAC1E;QAAE,MAAM;QAAqB,MAAM;IAA8B;IACjE;QAAE,MAAM;QAAsB,MAAM;IAA+B;CACpE;AAED,MAAM,UAAU;IACd;QAAE,MAAM;QAAY,MAAM;IAAS;IACnC;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAED,MAAM,YAAY;IAChB;QAAE,MAAM;QAAS,MAAM;IAAQ;IAC/B;QAAE,MAAM;QAAQ,MAAM;IAAQ;CAC/B;AAED,MAAM,SAAS;IACb;QACE,MAAM;QACN,MAAM;QACN,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;YAAC,MAAM;;;;;;IACxB;IACA;QACE,MAAM;QACN,MAAM;QACN,oBAAM,8OAAC,+IAAA,CAAA,aAAU;YAAC,MAAM;;;;;;IAC1B;CACD;AAED,MAAM,SAAS;IACb,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAkF;;;;;;sDAGhG,8OAAC;4CAAE,WAAU;sDAA2D;;;;;;;;;;;;8CAI1E,8OAAC;8CACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC;gDAAK,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DAAW;;;;;;0DAC3B,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlC,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDAGb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAI;oDACJ,KAAI;oDACJ,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;;;;;;0DAId,8OAAC;gDAAE,WAAU;0DAAwE;;;;;;0DAKrF,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0MAAA,CAAA,SAAM;oEAAC,MAAM;oEAAI,WAAU;;;;;;8EAC5B,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0MAAA,CAAA,SAAM;oEAAC,MAAM;oEAAI,WAAU;;;;;;8EAC5B,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kMAAA,CAAA,OAAI;oEAAC,MAAM;oEAAI,WAAU;;;;;;8EAC1B,8OAAC;oEAAE,MAAK;oEAAiC,WAAU;8EAA+D;;;;;;;;;;;;sEAEpH,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oMAAA,CAAA,QAAK;oEAAC,MAAM;oEAAI,WAAU;;;;;;8EAC3B,8OAAC;oEAAE,MAAK;oEAAkB,WAAU;8EAAqC;;;;;;;;;;;;sEAE3E,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oMAAA,CAAA,QAAK;oEAAC,MAAM;oEAAI,WAAU;;;;;;8EAC3B,8OAAC;oEAAE,MAAK;oEAAoB,WAAU;8EAAqC;;;;;;;;;;;;;;;;;;;;;;;0DAKjF,8OAAC;gDAAI,WAAU;0DACZ,OAAO,GAAG,CAAC,CAAC,kBACX,8OAAC;wDAAe,MAAM,EAAE,IAAI;wDAAE,cAAY,EAAE,IAAI;wDAAE,WAAU;kEACzD,EAAE,IAAI;uDADD,EAAE,IAAI;;;;;;;;;;;;;;;;kDAQpB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DAGb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAgD;;;;;;sEAC9D,8OAAC;4DAAG,WAAU;sEACX,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBACzB,8OAAC;8EACC,cAAA,8OAAC;wEAAE,MAAM,KAAK,IAAI;wEAAE,WAAU;kFAA4D,KAAK,IAAI;;;;;;mEAD5F,KAAK,IAAI;;;;;;;;;;;;;;;;8DAQxB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAgD;;;;;;sEAC9D,8OAAC;4DAAG,WAAU;sEACX,SAAS,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,qBACtB,8OAAC;8EACC,cAAA,8OAAC;wEAAE,MAAM,KAAK,IAAI;wEAAE,WAAU;kFAA4D,KAAK,IAAI;;;;;;mEAD5F,KAAK,IAAI;;;;;;;;;;;;;;;;8DAQxB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAgD;;;;;;sEAC9D,8OAAC;4DAAG,WAAU;sEACX,QAAQ,GAAG,CAAC,CAAC,qBACZ,8OAAC;8EACC,cAAA,8OAAC;wEAAE,MAAM,KAAK,IAAI;wEAAE,WAAU;kFAA4D,KAAK,IAAI;;;;;;mEAD5F,KAAK,IAAI;;;;;;;;;;sEAMtB,8OAAC;4DAAG,WAAU;sEAAqD;;;;;;sEACnE,8OAAC;4DAAG,WAAU;sEACX,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC;8EACC,cAAA,8OAAC;wEAAE,MAAM,KAAK,IAAI;wEAAE,WAAU;kFAA4D,KAAK,IAAI;;;;;;mEAD5F,KAAK,IAAI;;;;;;;;;;;;;;;;8DAQxB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAmE;;;;;;sEACjF,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4EACJ,KAAI;4EACJ,KAAI;4EACJ,OAAO;4EACP,QAAQ;4EACR,WAAU;;;;;;;;;;;;;;;;8EAIhB,8OAAC;oEAAE,WAAU;8EAAyD;;;;;;8EAGtE,8OAAC;oEACC,MAAK;oEACL,QAAO;oEACP,KAAI;oEACJ,WAAU;;sFAEV,8OAAC;4EAAK,WAAU;sFAAW;;;;;;sFAC3B,8OAAC,sNAAA,CAAA,eAAY;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASpC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;wCAAqC;wCAC7C,IAAI,OAAO,WAAW;wCAAG;sDAC5B,8OAAC;4CAAE,MAAK;4CAAW,WAAU;sDAA0C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvF;uCAEe", "debugId": null}}, {"offset": {"line": 790, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/layout.js"], "sourcesContent": ["// src/app/layout.js\r\n\r\nimport { Poppins } from \"next/font/google\";\r\nimport \"./globals.css\";\r\nimport Header from \"@/app/common/Header\";\r\nimport Footer from \"@/app/common/Footer\";\r\n\r\n// Configure the Poppins font\r\nconst poppins = Poppins({\r\n  subsets: [\"latin\"],\r\n  display: \"swap\",\r\n  weight: [\"100\", \"200\", \"300\", \"400\", \"500\", \"600\", \"700\", \"800\", \"900\"],\r\n});\r\n\r\nexport const metadata = {\r\n  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'https://securitylit.com'),\r\n  title: \"Premier Cybersecurity Consulting & VAPT Services | SecurityLit\",\r\n  description: \"SecurityLit offers expert VAPT, compliance, and cloud security consulting to protect businesses in Gurugram, Noida, Mumbai, and Bangalore from digital threats.\",\r\n  keywords: [\r\n    \"cybersecurity consulting\",\r\n    \"VAPT services\",\r\n    \"penetration testing\",\r\n    \"vulnerability assessment\",\r\n    \"cloud security\",\r\n    \"compliance assessment\",\r\n    \"red teaming\",\r\n    \"virtual CISO\",\r\n    \"security audit\",\r\n    \"India cybersecurity\"\r\n  ],\r\n  authors: [{ name: \"SecurityLit Team\" }],\r\n  creator: \"SecurityLit\",\r\n  publisher: \"SecurityLit\",\r\n  formatDetection: {\r\n    email: false,\r\n    address: false,\r\n    telephone: false,\r\n  },\r\n  openGraph: {\r\n    type: \"website\",\r\n    locale: \"en_US\",\r\n    url: \"/\",\r\n    title: \"Premier Cybersecurity Consulting & VAPT Services | SecurityLit\",\r\n    description: \"SecurityLit offers expert VAPT, compliance, and cloud security consulting to protect businesses in Gurugram, Noida, Mumbai, and Bangalore from digital threats.\",\r\n    siteName: \"SecurityLit\",\r\n    images: [\r\n      {\r\n        url: \"/og-image.png\",\r\n        width: 1200,\r\n        height: 630,\r\n        alt: \"SecurityLit - Premier Cybersecurity Consulting Services\",\r\n      },\r\n    ],\r\n  },\r\n  twitter: {\r\n    card: \"summary_large_image\",\r\n    title: \"Premier Cybersecurity Consulting & VAPT Services | SecurityLit\",\r\n    description: \"SecurityLit offers expert VAPT, compliance, and cloud security consulting to protect businesses in Gurugram, Noida, Mumbai, and Bangalore from digital threats.\",\r\n    creator: \"@security_lit\",\r\n    images: [\"/twitter-image.png\"],\r\n  },\r\n  robots: {\r\n    index: true,\r\n    follow: true,\r\n    googleBot: {\r\n      index: true,\r\n      follow: true,\r\n      \"max-video-preview\": -1,\r\n      \"max-image-preview\": \"large\",\r\n      \"max-snippet\": -1,\r\n    },\r\n  },\r\n  verification: {\r\n    google: process.env.GOOGLE_SITE_VERIFICATION,\r\n  },\r\n};\r\n\r\nexport default function RootLayout({ children }) {\r\n  return (\r\n    <html lang=\"en\">\r\n      <head>\r\n        <meta charSet=\"utf-8\" />\r\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />\r\n        <meta name=\"theme-color\" content=\"#3265fe\" />\r\n      </head>\r\n      <body className={`${poppins.className} antialiased`}>\r\n        <Header />\r\n        <main>{children}</main>\r\n        <Footer />\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,oBAAoB;;;;;;;AAIpB;AACA;;;;;;AASO,MAAM,WAAW;IACtB,cAAc,IAAI,IAAI,+DAAoC;IAC1D,OAAO;IACP,aAAa;IACb,UAAU;QACR;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,SAAS;QAAC;YAAE,MAAM;QAAmB;KAAE;IACvC,SAAS;IACT,WAAW;IACX,iBAAiB;QACf,OAAO;QACP,SAAS;QACT,WAAW;IACb;IACA,WAAW;QACT,MAAM;QACN,QAAQ;QACR,KAAK;QACL,OAAO;QACP,aAAa;QACb,UAAU;QACV,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;IACH;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,SAAS;QACT,QAAQ;YAAC;SAAqB;IAChC;IACA,QAAQ;QACN,OAAO;QACP,QAAQ;QACR,WAAW;YACT,OAAO;YACP,QAAQ;YACR,qBAAqB,CAAC;YACtB,qBAAqB;YACrB,eAAe,CAAC;QAClB;IACF;IACA,cAAc;QACZ,QAAQ,QAAQ,GAAG,CAAC,wBAAwB;IAC9C;AACF;AAEe,SAAS,WAAW,EAAE,QAAQ,EAAE;IAC7C,qBACE,8OAAC;QAAK,MAAK;;0BACT,8OAAC;;kCACC,8OAAC;wBAAK,SAAQ;;;;;;kCACd,8OAAC;wBAAK,MAAK;wBAAW,SAAQ;;;;;;kCAC9B,8OAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;;;;;;;0BAEnC,8OAAC;gBAAK,WAAW,GAAG,2IAAA,CAAA,UAAO,CAAC,SAAS,CAAC,YAAY,CAAC;;kCACjD,8OAAC,+HAAA,CAAA,UAAM;;;;;kCACP,8OAAC;kCAAM;;;;;;kCACP,8OAAC,+HAAA,CAAA,UAAM;;;;;;;;;;;;;;;;;AAIf", "debugId": null}}]}