{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/poppins_1bffe7f2.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"poppins_1bffe7f2-module__MT77oa__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 11, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/poppins_1bffe7f2.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.js%22,%22import%22:%22Poppins%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22display%22:%22swap%22,%22weight%22:[%22100%22,%22200%22,%22300%22,%22400%22,%22500%22,%22600%22,%22700%22,%22800%22,%22900%22]}],%22variableName%22:%22poppins%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Poppins', 'Poppins Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,uJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,uJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,uJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/common/Header.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/common/Header.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/common/Header.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/common/Header.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/common/Header.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/common/Header.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/layout.js"], "sourcesContent": ["// src/app/layout.js\r\n\r\nimport { Poppins } from \"next/font/google\";\r\nimport \"./globals.css\";\r\nimport Header from \"@/app/common/Header\";\r\nimport Footer from \"@/app/common/Footer\";\r\n\r\n// Configure the Poppins font\r\nconst poppins = Poppins({\r\n  subsets: [\"latin\"],\r\n  display: \"swap\",\r\n  weight: [\"100\", \"200\", \"300\", \"400\", \"500\", \"600\", \"700\", \"800\", \"900\"],\r\n});\r\n\r\nexport const metadata = {\r\n  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'https://securitylit.com'),\r\n  title: \"Premier Cybersecurity Consulting & VAPT Services | SecurityLit\",\r\n  description: \"SecurityLit offers expert VAPT, compliance, and cloud security consulting to protect businesses in Gurugram, Noida, Mumbai, and Bangalore from digital threats.\",\r\n  keywords: [\r\n    \"cybersecurity consulting\",\r\n    \"VAPT services\",\r\n    \"penetration testing\",\r\n    \"vulnerability assessment\",\r\n    \"cloud security\",\r\n    \"compliance assessment\",\r\n    \"red teaming\",\r\n    \"virtual CISO\",\r\n    \"security audit\",\r\n    \"India cybersecurity\"\r\n  ],\r\n  authors: [{ name: \"SecurityLit Team\" }],\r\n  creator: \"SecurityLit\",\r\n  publisher: \"SecurityLit\",\r\n  formatDetection: {\r\n    email: false,\r\n    address: false,\r\n    telephone: false,\r\n  },\r\n  openGraph: {\r\n    type: \"website\",\r\n    locale: \"en_US\",\r\n    url: \"/\",\r\n    title: \"Premier Cybersecurity Consulting & VAPT Services | SecurityLit\",\r\n    description: \"SecurityLit offers expert VAPT, compliance, and cloud security consulting to protect businesses in Gurugram, Noida, Mumbai, and Bangalore from digital threats.\",\r\n    siteName: \"SecurityLit\",\r\n    images: [\r\n      {\r\n        url: \"/og-image.png\",\r\n        width: 1200,\r\n        height: 630,\r\n        alt: \"SecurityLit - Premier Cybersecurity Consulting Services\",\r\n      },\r\n    ],\r\n  },\r\n  twitter: {\r\n    card: \"summary_large_image\",\r\n    title: \"Premier Cybersecurity Consulting & VAPT Services | SecurityLit\",\r\n    description: \"SecurityLit offers expert VAPT, compliance, and cloud security consulting to protect businesses in Gurugram, Noida, Mumbai, and Bangalore from digital threats.\",\r\n    creator: \"@security_lit\",\r\n    images: [\"/twitter-image.png\"],\r\n  },\r\n  robots: {\r\n    index: true,\r\n    follow: true,\r\n    googleBot: {\r\n      index: true,\r\n      follow: true,\r\n      \"max-video-preview\": -1,\r\n      \"max-image-preview\": \"large\",\r\n      \"max-snippet\": -1,\r\n    },\r\n  },\r\n  verification: {\r\n    google: process.env.GOOGLE_SITE_VERIFICATION,\r\n  },\r\n};\r\n\r\nexport default function RootLayout({ children }) {\r\n  return (\r\n    <html lang=\"en\">\r\n      <head>\r\n        <meta charSet=\"utf-8\" />\r\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />\r\n        <meta name=\"theme-color\" content=\"#3265fe\" />\r\n      </head>\r\n      <body className={`${poppins.className} antialiased`}>\r\n        <Header />\r\n        <main>{children}</main>\r\n        <Footer />\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,oBAAoB;;;;;;;AAIpB;AACA;;;;;;AASO,MAAM,WAAW;IACtB,cAAc,IAAI,IAAI,+DAAoC;IAC1D,OAAO;IACP,aAAa;IACb,UAAU;QACR;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,SAAS;QAAC;YAAE,MAAM;QAAmB;KAAE;IACvC,SAAS;IACT,WAAW;IACX,iBAAiB;QACf,OAAO;QACP,SAAS;QACT,WAAW;IACb;IACA,WAAW;QACT,MAAM;QACN,QAAQ;QACR,KAAK;QACL,OAAO;QACP,aAAa;QACb,UAAU;QACV,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;IACH;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,SAAS;QACT,QAAQ;YAAC;SAAqB;IAChC;IACA,QAAQ;QACN,OAAO;QACP,QAAQ;QACR,WAAW;YACT,OAAO;YACP,QAAQ;YACR,qBAAqB,CAAC;YACtB,qBAAqB;YACrB,eAAe,CAAC;QAClB;IACF;IACA,cAAc;QACZ,QAAQ,QAAQ,GAAG,CAAC,wBAAwB;IAC9C;AACF;AAEe,SAAS,WAAW,EAAE,QAAQ,EAAE;IAC7C,qBACE,8OAAC;QAAK,MAAK;;0BACT,8OAAC;;kCACC,8OAAC;wBAAK,SAAQ;;;;;;kCACd,8OAAC;wBAAK,MAAK;wBAAW,SAAQ;;;;;;kCAC9B,8OAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;;;;;;;0BAEnC,8OAAC;gBAAK,WAAW,GAAG,2IAAA,CAAA,UAAO,CAAC,SAAS,CAAC,YAAY,CAAC;;kCACjD,8OAAC,+HAAA,CAAA,UAAM;;;;;kCACP,8OAAC;kCAAM;;;;;;kCACP,8OAAC,+HAAA,CAAA,UAAM;;;;;;;;;;;;;;;;;AAIf", "debugId": null}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}