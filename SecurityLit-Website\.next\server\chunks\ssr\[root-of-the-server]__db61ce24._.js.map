{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/privacy/components/privacyPolicy.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/privacy/components/privacyPolicy.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/privacy/components/privacyPolicy.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgT,GAC7U,8EACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/privacy/components/privacyPolicy.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/privacy/components/privacyPolicy.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/privacy/components/privacyPolicy.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4R,GACzT,0DACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/privacy/page.js"], "sourcesContent": ["import PrivacyPolicy from './components/privacyPolicy';\r\n\r\nexport const metadata = {\r\n  title: \"Privacy Policy | SecurityLit - Cybersecurity Services\",\r\n  description: \"SecurityLit's privacy policy outlines how we collect, use, and protect your personal information. Learn about our data practices and your rights under the New Zealand Privacy Act 1993.\",\r\n  keywords: \"privacy policy, SecurityLit, data protection, personal information, cybersecurity privacy, New Zealand Privacy Act, data security\",\r\n  robots: \"index, follow\",\r\n  icons: {\r\n    icon: '/favicon.ico',\r\n  },\r\n  openGraph: {\r\n    title: \"Privacy Policy | SecurityLit - Cybersecurity Services\",\r\n    type: \"website\",\r\n    url: \"https://securitylit.com/privacy\",\r\n    description: \"SecurityLit's privacy policy outlines how we collect, use, and protect your personal information. Learn about our data practices and your rights under the New Zealand Privacy Act 1993.\",\r\n    images: \"/seclit-logo-1.png\",\r\n  },\r\n  twitter: {\r\n    card: 'summary_large_image',\r\n    title: 'Privacy Policy | SecurityLit - Cybersecurity Services',\r\n    description: 'SecurityLit\\'s privacy policy outlines how we collect, use, and protect your personal information. Learn about our data practices and your rights under the New Zealand Privacy Act 1993.',\r\n    images: \"/seclit-logo-1.png\",\r\n  }\r\n};\r\n\r\nexport default function PrivacyPage() {\r\n  return (\r\n    <div className=\"min-h-screen\">\r\n      <PrivacyPolicy />\r\n    </div>\r\n  );\r\n} "], "names": [], "mappings": ";;;;;AAAA;;;AAEO,MAAM,WAAW;IACtB,OAAO;IACP,aAAa;IACb,UAAU;IACV,QAAQ;IACR,OAAO;QACL,MAAM;IACR;IACA,WAAW;QACT,OAAO;QACP,MAAM;QACN,KAAK;QACL,aAAa;QACb,QAAQ;IACV;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,QAAQ;IACV;AACF;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,qJAAA,CAAA,UAAa;;;;;;;;;;AAGpB", "debugId": null}}]}