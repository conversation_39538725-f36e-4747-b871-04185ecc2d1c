{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/blogs/components/BlogView/ContentSection.jsx"], "sourcesContent": ["import React, { Children, cloneElement, useEffect, useRef } from 'react';\r\n\r\nconst ContentSection = ({ children, toc, onTocGenerated }) => {\r\n  // Function to generate TOC from content\r\n  const generateTocFromContent = (content) => {\r\n    const tocItems = [];\r\n    let currentH1 = null;\r\n    let currentH2 = null;\r\n\r\n    Children.forEach(content, (child) => {\r\n      if (child && (child.type === 'h1' || child.type === 'h2' || child.type === 'h3') && child.props && child.props.children) {\r\n        const text = typeof child.props.children === 'string' ? child.props.children : (Array.isArray(child.props.children) ? child.props.children.join(' ') : '');\r\n        const id = text.toLowerCase()\r\n          .replace(/[^a-z0-9\\s]+/g, '') // Remove special characters but keep spaces\r\n          .replace(/\\s+/g, '-') // Replace spaces with hyphens\r\n          .replace(/(^-|-$)/g, ''); // Remove leading/trailing hyphens\r\n\r\n        if (child.type === 'h1') {\r\n          currentH1 = { id: id, text, subItems: [] };\r\n          tocItems.push(currentH1);\r\n          currentH2 = null; // Reset H2 when new H1\r\n        } else if (child.type === 'h2') {\r\n          currentH2 = { id: id, text, subItems: [] };\r\n          if (currentH1) {\r\n            currentH1.subItems.push(currentH2);\r\n          } else {\r\n            tocItems.push(currentH2);\r\n          }\r\n        } else if (child.type === 'h3') {\r\n          const h3Item = { id: id, text };\r\n          if (currentH2) {\r\n            currentH2.subItems.push(h3Item);\r\n          } else if (currentH1) {\r\n            currentH1.subItems.push(h3Item);\r\n          } else {\r\n            tocItems.push(h3Item);\r\n          }\r\n        }\r\n      }\r\n    });\r\n\r\n    return tocItems;\r\n  };\r\n\r\n  // Generate TOC when component mounts or children change\r\n  const lastTocRef = useRef();\r\n  useEffect(() => {\r\n    if (onTocGenerated) {\r\n      const autoGeneratedToc = generateTocFromContent(children);\r\n      console.log('📋 Generated TOC:', autoGeneratedToc);\r\n      // Only call if TOC actually changed\r\n      if (JSON.stringify(lastTocRef.current) !== JSON.stringify(autoGeneratedToc)) {\r\n        lastTocRef.current = autoGeneratedToc;\r\n        onTocGenerated(autoGeneratedToc);\r\n      }\r\n    }\r\n  }, [children, onTocGenerated]);\r\n\r\n  // Add id to all h1, h2, and h3s for anchor navigation\r\n  const enhancedChildren = Children.map(children, (child) => {\r\n    if (child && (child.type === 'h1' || child.type === 'h2' || child.type === 'h3') && child.props && child.props.children) {\r\n      // Generate id from text using the same logic as TOC generation\r\n      const text = typeof child.props.children === 'string' ? child.props.children : (Array.isArray(child.props.children) ? child.props.children.join(' ') : '');\r\n      const id = text.toLowerCase()\r\n        .replace(/[^a-z0-9\\s]+/g, '') // Remove special characters but keep spaces\r\n        .replace(/\\s+/g, '-') // Replace spaces with hyphens\r\n        .replace(/(^-|-$)/g, ''); // Remove leading/trailing hyphens\r\n      \r\n      console.log('🏷️ Generated ID for heading:', { text: text.slice(0, 50), id });\r\n      \r\n      return cloneElement(child, { \r\n        id: id,\r\n        className: `${child.props.className || ''} scroll-mt-32` // Add scroll margin for navbar offset\r\n      });\r\n    }\r\n    return child;\r\n  });\r\n  \r\n  return (\r\n    <div className=\"bg-white sm:mx-8 md:p-14 p-6 flex flex-col gap-4 rounded-2xl shadow-md my-8 border border-[var(--color-yellow)]\" style={{ scrollBehavior: 'smooth' }}>\r\n      {enhancedChildren}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ContentSection;"], "names": [], "mappings": ";;;;AAAA;;;AAEA,MAAM,iBAAiB,CAAC,EAAE,QAAQ,EAAE,GAAG,EAAE,cAAc,EAAE;IACvD,wCAAwC;IACxC,MAAM,yBAAyB,CAAC;QAC9B,MAAM,WAAW,EAAE;QACnB,IAAI,YAAY;QAChB,IAAI,YAAY;QAEhB,qMAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,SAAS,CAAC;YACzB,IAAI,SAAS,CAAC,MAAM,IAAI,KAAK,QAAQ,MAAM,IAAI,KAAK,QAAQ,MAAM,IAAI,KAAK,IAAI,KAAK,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC,QAAQ,EAAE;gBACvH,MAAM,OAAO,OAAO,MAAM,KAAK,CAAC,QAAQ,KAAK,WAAW,MAAM,KAAK,CAAC,QAAQ,GAAI,MAAM,OAAO,CAAC,MAAM,KAAK,CAAC,QAAQ,IAAI,MAAM,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO;gBACvJ,MAAM,KAAK,KAAK,WAAW,GACxB,OAAO,CAAC,iBAAiB,IAAI,4CAA4C;iBACzE,OAAO,CAAC,QAAQ,KAAK,8BAA8B;iBACnD,OAAO,CAAC,YAAY,KAAK,kCAAkC;gBAE9D,IAAI,MAAM,IAAI,KAAK,MAAM;oBACvB,YAAY;wBAAE,IAAI;wBAAI;wBAAM,UAAU,EAAE;oBAAC;oBACzC,SAAS,IAAI,CAAC;oBACd,YAAY,MAAM,uBAAuB;gBAC3C,OAAO,IAAI,MAAM,IAAI,KAAK,MAAM;oBAC9B,YAAY;wBAAE,IAAI;wBAAI;wBAAM,UAAU,EAAE;oBAAC;oBACzC,IAAI,WAAW;wBACb,UAAU,QAAQ,CAAC,IAAI,CAAC;oBAC1B,OAAO;wBACL,SAAS,IAAI,CAAC;oBAChB;gBACF,OAAO,IAAI,MAAM,IAAI,KAAK,MAAM;oBAC9B,MAAM,SAAS;wBAAE,IAAI;wBAAI;oBAAK;oBAC9B,IAAI,WAAW;wBACb,UAAU,QAAQ,CAAC,IAAI,CAAC;oBAC1B,OAAO,IAAI,WAAW;wBACpB,UAAU,QAAQ,CAAC,IAAI,CAAC;oBAC1B,OAAO;wBACL,SAAS,IAAI,CAAC;oBAChB;gBACF;YACF;QACF;QAEA,OAAO;IACT;IAEA,wDAAwD;IACxD,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IACxB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB;YAClB,MAAM,mBAAmB,uBAAuB;YAChD,QAAQ,GAAG,CAAC,qBAAqB;YACjC,oCAAoC;YACpC,IAAI,KAAK,SAAS,CAAC,WAAW,OAAO,MAAM,KAAK,SAAS,CAAC,mBAAmB;gBAC3E,WAAW,OAAO,GAAG;gBACrB,eAAe;YACjB;QACF;IACF,GAAG;QAAC;QAAU;KAAe;IAE7B,sDAAsD;IACtD,MAAM,mBAAmB,qMAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,UAAU,CAAC;QAC/C,IAAI,SAAS,CAAC,MAAM,IAAI,KAAK,QAAQ,MAAM,IAAI,KAAK,QAAQ,MAAM,IAAI,KAAK,IAAI,KAAK,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC,QAAQ,EAAE;YACvH,+DAA+D;YAC/D,MAAM,OAAO,OAAO,MAAM,KAAK,CAAC,QAAQ,KAAK,WAAW,MAAM,KAAK,CAAC,QAAQ,GAAI,MAAM,OAAO,CAAC,MAAM,KAAK,CAAC,QAAQ,IAAI,MAAM,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO;YACvJ,MAAM,KAAK,KAAK,WAAW,GACxB,OAAO,CAAC,iBAAiB,IAAI,4CAA4C;aACzE,OAAO,CAAC,QAAQ,KAAK,8BAA8B;aACnD,OAAO,CAAC,YAAY,KAAK,kCAAkC;YAE9D,QAAQ,GAAG,CAAC,iCAAiC;gBAAE,MAAM,KAAK,KAAK,CAAC,GAAG;gBAAK;YAAG;YAE3E,qBAAO,CAAA,GAAA,qMAAA,CAAA,eAAY,AAAD,EAAE,OAAO;gBACzB,IAAI;gBACJ,WAAW,GAAG,MAAM,KAAK,CAAC,SAAS,IAAI,GAAG,aAAa,CAAC,CAAC,sCAAsC;YACjG;QACF;QACA,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;QAAkH,OAAO;YAAE,gBAAgB;QAAS;kBAChK;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/blogs/components/BlogView/HeaderSection.jsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport Image from 'next/image';\r\n\r\nconst HeaderSection = ({ title, description, imageUrl, breadcrumbs, toc }) => {\r\n  const [showToc, setShowToc] = useState(false);\r\n  return (\r\n    <div className=\"bg-[var(--color-dark-blue)] text-white relative px-0 md:px-0 py-0 flex flex-col lg:flex-row items-stretch gap-0\">\r\n      {/* Breadcrumbs positioned absolutely at the top */}\r\n      {breadcrumbs && (\r\n        <div className=\"absolute top-0 left-0 right-0 pt-2 sm:pt-2 md:pt-5 xl:pt-14 z-10\">\r\n          <div className=\"max-w-7xl px-2 sm:px-2 md:px-16\">\r\n            {breadcrumbs}\r\n          </div>\r\n        </div>\r\n      )}\r\n      {/* Mobile: Image first, then text */}\r\n      <div className=\"lg:hidden w-full flex flex-col items-center gap-4 sm:gap-6 px-4 sm:px-6 md:px-8 py-6 sm:py-8 lg:py-12 bg-transparent mt-24 sm:mt-28 md:mt-32\">\r\n        {/* Image at the top for mobile */}\r\n        {imageUrl && (\r\n          <div className=\"w-full max-w-sm sm:max-w-md aspect-[4/3] rounded-xl overflow-hidden shadow-lg bg-white border border-[var(--color-yellow)] flex items-center justify-center\">\r\n            <Image\r\n              src={imageUrl}\r\n              alt={title || \"Blog post featured image\"}\r\n              className=\"object-contain w-full h-full\"\r\n              width={400}\r\n              height={300}\r\n              priority\r\n            />\r\n          </div>\r\n        )}\r\n        {/* Text content below image for mobile */}\r\n        <div className=\"flex flex-col justify-center text-center px-2\">\r\n          <h1 className=\"text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold mb-3 sm:mb-4 leading-tight\">{title}</h1>\r\n          <p className=\"text-sm sm:text-base md:text-lg text-white/90 max-w-2xl leading-relaxed\">{description}</p>\r\n        </div>\r\n      </div>\r\n      \r\n      {/* Desktop: Text and image side by side */}\r\n      <div className=\"hidden lg:flex flex-1 min-w-0 flex-col justify-center px-6 md:px-16 py-12 lg:py-20 mt-24 xl:mt-28\">\r\n        <h1 className=\"text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold mb-4 leading-tight\">{title}</h1>\r\n        <p className=\"text-base md:text-lg lg:text-xl text-white/90 max-w-2xl leading-relaxed\">{description}</p>\r\n      </div>\r\n      \r\n      {/* Desktop: Right Sidebar - Image and TOC */}\r\n      <aside className=\"hidden lg:flex flex-col justify-center items-center gap-6 px-6 md:px-8 py-8 lg:py-12 bg-transparent lg:bg-white/0 mt-24 xl:mt-28 w-full lg:w-[380px] flex-shrink-0\">\r\n        {/* Image always at the top */}\r\n        {imageUrl && (\r\n          <div className=\"w-full aspect-[4/3] rounded-xl overflow-hidden shadow-lg bg-white border border-[var(--color-yellow)] flex items-center justify-center\">\r\n            <Image\r\n              src={imageUrl}\r\n              alt={title || \"Blog post featured image\"}\r\n              className=\"object-contain w-full h-full\"\r\n              width={340}\r\n              height={255}\r\n              priority\r\n            />\r\n          </div>\r\n        )}\r\n        {/* TOC for desktop (always visible) */}\r\n        {toc && toc.length > 0 && (\r\n          <div className=\"hidden lg:block w-full\">\r\n            <div className=\"bg-white rounded-xl shadow-lg border border-[var(--color-yellow)] p-6\">\r\n              <h3 className=\"text-lg font-semibold mb-3 text-[var(--color-dark-blue)]\">Table of Contents</h3>\r\n              <ul className=\"space-y-2\">\r\n                {toc.map((item) => (\r\n                  <li key={item.id}>\r\n                    <a href={`#${item.id}`} className=\"text-[var(--color-blue)] hover:underline text-sm block truncate\">\r\n                      {item.text}\r\n                    </a>\r\n                  </li>\r\n                ))}\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        )}\r\n        {/* TOC for mobile (collapsible, more prominent) */}\r\n        {toc && toc.length > 0 && (\r\n          <div className=\"block lg:hidden w-full\">\r\n            <button\r\n              className=\"w-full px-4 py-2 bg-white/20 text-white rounded-lg font-semibold mb-2 border border-[var(--color-yellow)]\"\r\n              onClick={() => setShowToc((v) => !v)}\r\n            >\r\n              {showToc ? 'Hide' : 'Show'} Table of Contents\r\n            </button>\r\n            {showToc && (\r\n              <div className=\"bg-white rounded-xl shadow-lg border border-[var(--color-yellow)] p-6 mb-2\">\r\n                <h3 className=\"text-lg font-semibold mb-3 text-[var(--color-dark-blue)]\">Table of Contents</h3>\r\n                <ul className=\"space-y-2\">\r\n                  {toc.map((item) => (\r\n                    <li key={item.id}>\r\n                      <a href={`#${item.id}`} className=\"text-[var(--color-blue)] hover:underline text-sm block truncate\">\r\n                        {item.text}\r\n                      </a>\r\n                    </li>\r\n                  ))}\r\n                </ul>\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n      </aside>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default HeaderSection;"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,gBAAgB,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,EAAE;IACvE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,qBACE,8OAAC;QAAI,WAAU;;YAEZ,6BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;0BAKP,8OAAC;gBAAI,WAAU;;oBAEZ,0BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAK;4BACL,KAAK,SAAS;4BACd,WAAU;4BACV,OAAO;4BACP,QAAQ;4BACR,QAAQ;;;;;;;;;;;kCAKd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoF;;;;;;0CAClG,8OAAC;gCAAE,WAAU;0CAA2E;;;;;;;;;;;;;;;;;;0BAK5F,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6E;;;;;;kCAC3F,8OAAC;wBAAE,WAAU;kCAA2E;;;;;;;;;;;;0BAI1F,8OAAC;gBAAM,WAAU;;oBAEd,0BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAK;4BACL,KAAK,SAAS;4BACd,WAAU;4BACV,OAAO;4BACP,QAAQ;4BACR,QAAQ;;;;;;;;;;;oBAKb,OAAO,IAAI,MAAM,GAAG,mBACnB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2D;;;;;;8CACzE,8OAAC;oCAAG,WAAU;8CACX,IAAI,GAAG,CAAC,CAAC,qBACR,8OAAC;sDACC,cAAA,8OAAC;gDAAE,MAAM,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;gDAAE,WAAU;0DAC/B,KAAK,IAAI;;;;;;2CAFL,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;oBAWzB,OAAO,IAAI,MAAM,GAAG,mBACnB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,WAAU;gCACV,SAAS,IAAM,WAAW,CAAC,IAAM,CAAC;;oCAEjC,UAAU,SAAS;oCAAO;;;;;;;4BAE5B,yBACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2D;;;;;;kDACzE,8OAAC;wCAAG,WAAU;kDACX,IAAI,GAAG,CAAC,CAAC,qBACR,8OAAC;0DACC,cAAA,8OAAC;oDAAE,MAAM,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;oDAAE,WAAU;8DAC/B,KAAK,IAAI;;;;;;+CAFL,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcpC;uCAEe", "debugId": null}}, {"offset": {"line": 365, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/blogs/components/BlogView/FullBlogView.jsx"], "sourcesContent": ["\"use client\";\nimport React, { useState, useEffect } from 'react';\nimport { usePathname } from 'next/navigation';\nimport ContentSection from './ContentSection';\nimport HeaderSection from './HeaderSection';\nimport Image from 'next/image';\n// BreadcrumbNavigation can be implemented or imported as needed\n\nconst FullBlogView = ({ headerSection, children, breadcrumbs, blogTitle, toc: manualToc }) => {\n  const pathname = usePathname();\n  const [autoGeneratedToc, setAutoGeneratedToc] = useState([]);\n  const [activeSection, setActiveSection] = useState(\"\");\n  const [isSticky, setIsSticky] = useState(false);\n  const [tocPosition, setTocPosition] = useState({ top: 0, left: 0 });\n\n  // Use auto-generated TOC if available, otherwise fall back to manual TOC\n  const toc = autoGeneratedToc.length > 0 ? autoGeneratedToc : manualToc;\n\n  // Scroll offset to account for fixed navbar (same as privacy policy)\n  const SCROLL_OFFSET = 128;\n\n  // Extract blog title from URL path if not provided\n  const extractTitleFromPath = (path) => {\n    const segments = path.split('/');\n    const blogSlug = segments[segments.length - 1];\n    return blogSlug\n      .split('-')\n      .map(word => word.charAt(0).toUpperCase() + word.slice(1))\n      .join(' ');\n  };\n\n  const title = blogTitle || extractTitleFromPath(pathname);\n\n  // Generate breadcrumbs if not provided\n  const blogBreadcrumbs = breadcrumbs || [\n    {\n      name: \"Home\",\n      url: \"/\",\n      iconKey: \"home\",\n      description: \"Return to homepage\"\n    },\n    {\n      name: \"Blogs\",\n      url: \"/blogs\",\n      iconKey: \"blogs\",\n      description: \"Cybersecurity insights and penetration testing guidance\"\n    },\n    {\n      name: title,\n      url: pathname,\n      current: true,\n      description: `${title} - Expert cybersecurity insights and penetration testing guidance`\n    }\n  ];\n\n  // Sidebar content (image, TOC, author, related)\n  const { imageUrl, author, relatedArticles } = headerSection || {};\n\n  // Set up intersection observer for active section tracking\n  useEffect(() => {\n    if (!toc || toc.length === 0) return;\n\n    const observer = new IntersectionObserver(\n      (entries) => {\n        entries.forEach((entry) => {\n          if (entry.isIntersecting) {\n            setActiveSection(entry.target.id);\n          }\n        });\n      },\n      { rootMargin: `-${SCROLL_OFFSET}px 0px -80% 0px`, threshold: 0 }\n    );\n\n    // Collect all section IDs from TOC\n    const collectIds = (items) => {\n      let ids = [];\n      items.forEach(item => {\n        ids.push(item.id);\n        if (item.subItems) {\n          ids = ids.concat(collectIds(item.subItems));\n        }\n      });\n      return ids;\n    };\n\n    const allIds = collectIds(toc);\n\n    allIds.forEach((id) => {\n      const element = document.getElementById(id);\n      if (element) {\n        observer.observe(element);\n      }\n    });\n\n    return () => observer.disconnect();\n  }, [toc, SCROLL_OFFSET]);\n\n  // Handle sticky TOC behavior\n  useEffect(() => {\n    const handleScroll = () => {\n      const scrollY = window.scrollY;\n      const windowHeight = window.innerHeight;\n      const documentHeight = document.documentElement.scrollHeight;\n\n      // Simple scroll-based logic:\n      // - Become sticky after scrolling 400px (past hero section)\n      // - Stop being sticky when near bottom (within 500px of end)\n      const shouldBeSticky = scrollY > 400 && (documentHeight - scrollY - windowHeight) > 500;\n\n      setIsSticky(shouldBeSticky);\n\n      if (shouldBeSticky) {\n        // Calculate left position for sticky TOC\n        const viewportWidth = window.innerWidth;\n        const contentMaxWidth = 1280; // max-w-7xl = 80rem = 1280px\n        const leftPosition = Math.max(16, (viewportWidth - contentMaxWidth) / 2);\n        setTocPosition({\n          top: 96, // Below navbar\n          left: leftPosition\n        });\n      }\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    window.addEventListener('resize', handleScroll);\n    handleScroll(); // Initial check\n\n    return () => {\n      window.removeEventListener('scroll', handleScroll);\n      window.removeEventListener('resize', handleScroll);\n    };\n  }, []);\n\n  // Callback to receive auto-generated TOC from ContentSection\n  const handleTocGenerated = (generatedToc) => {\n    setAutoGeneratedToc(generatedToc);\n  };\n\n  // Handle TOC link clicks with smooth scrolling\n  const handleTocClick = (e, targetId) => {\n    e.preventDefault();\n    // Wait a moment for any dynamic content to render\n    setTimeout(() => {\n      // Try to find element by ID first\n      let targetElement = document.getElementById(targetId);\n      // Check if element is actually visible (has dimensions)\n      if (targetElement) {\n        const rect = targetElement.getBoundingClientRect();\n        // If element has zero dimensions, it's likely in a hidden container (lg:hidden on desktop)\n        if (rect.width === 0 && rect.height === 0) {\n          targetElement = null; // Force search for visible element\n        }\n      }\n      // If element not found or not visible, try alternative approaches\n      if (!targetElement) {\n        // Search for visible elements with the same ID or text content\n        const isDesktop = window.innerWidth >= 1024;\n        if (isDesktop) {\n          // On desktop, look for elements NOT in lg:hidden containers\n          const allHeadings = document.querySelectorAll('h1, h2, h3');\n          const targetText = targetId.replace(/-/g, ' ');\n          for (let heading of allHeadings) {\n            const rect = heading.getBoundingClientRect();\n            const headingText = heading.textContent?.toLowerCase() || '';\n            if (rect.width > 0 && rect.height > 0 && \n                (heading.id === targetId || headingText.includes(targetText.toLowerCase()))) {\n              targetElement = heading;\n              break;\n            }\n          }\n        } else {\n          // Mobile: use original search logic\n          const allHeadings = document.querySelectorAll('h1, h2, h3');\n          const targetText = targetId.replace(/-/g, ' ');\n          for (let heading of allHeadings) {\n            const headingText = heading.textContent?.toLowerCase() || '';\n            if (headingText.includes(targetText.toLowerCase())) {\n              targetElement = heading;\n              break;\n            }\n          }\n        }\n      }\n      if (targetElement) {\n        // Check if we're on desktop or mobile\n        const isDesktop = window.innerWidth >= 1024; // lg breakpoint\n        // Try to find if element is inside any scrollable containers\n        let scrollableParent = targetElement.parentElement;\n        while (scrollableParent) {\n          const style = window.getComputedStyle(scrollableParent);\n          if (style.overflow === 'auto' || style.overflow === 'scroll' || style.overflowY === 'auto' || style.overflowY === 'scroll') {\n            break;\n          }\n          scrollableParent = scrollableParent.parentElement;\n        }\n        if (isDesktop) {\n          // Method 1: Simple scrollIntoView with block center\n          targetElement.scrollIntoView({ \n            behavior: 'smooth', \n            block: 'center',\n            inline: 'nearest'\n          });\n          setTimeout(() => {\n            const afterRect = targetElement.getBoundingClientRect();\n            // If element is now visible, adjust for header\n            if (afterRect.top >= 0 && afterRect.top <= window.innerHeight) {\n              const adjustment = afterRect.top - 150; // Move element 150px from top\n              if (Math.abs(adjustment) > 10) { // Only adjust if significant\n                window.scrollBy({\n                  top: adjustment,\n                  behavior: 'smooth'\n                });\n              }\n            }\n          }, 100);\n        } else {\n          const offset = 120;\n          const rect = targetElement.getBoundingClientRect();\n          const currentScrollY = window.pageYOffset;\n          const elementAbsoluteTop = rect.top + currentScrollY;\n          const targetScrollPosition = elementAbsoluteTop - offset;\n          window.scrollTo({\n            top: targetScrollPosition,\n            behavior: 'smooth'\n          });\n        }\n        setTimeout(() => {\n          const finalRect = targetElement.getBoundingClientRect();\n          // If scroll didn't work properly, try scrollIntoView as fallback\n          if (finalRect.top > window.innerHeight - 100 || finalRect.top < 0) {\n            targetElement.scrollIntoView({\n              behavior: 'smooth',\n              block: 'start'\n            });\n            setTimeout(() => {\n              if (isDesktop) {\n                window.scrollBy({\n                  top: -50,\n                  behavior: 'smooth'\n                });\n              }\n            }, 100);\n          }\n        }, 800);\n      } else {\n        // As a fallback, try to scroll to the main content area\n        const mainContent = document.querySelector('.bg-white.sm\\\\:mx-8, .bg-white');\n        if (mainContent) {\n          mainContent.scrollIntoView({\n            behavior: 'smooth',\n            block: 'start'\n          });\n        }\n      }\n    }, 50); // Small delay to ensure DOM is ready\n  };\n\n  return (\n    <div className=\"min-h-screen flex flex-col\">\n      {/* Hero Section with integrated breadcrumbs */}\n      <HeaderSection\n        {...headerSection}\n        title={title}\n        breadcrumbs={null /* Add breadcrumbs here if needed */}\n        toc={null} // Remove TOC from header\n      />\n      \n      {/* Mobile Layout: TOC first, then content */}\n      <div className=\"lg:hidden w-full max-w-7xl mx-auto px-6 pb-8 flex flex-col gap-6 bg-white\">\n        {/* TOC above blog content for mobile */}\n        {toc && toc.length > 0 && (\n          <div className=\"bg-gray-50 rounded-xl shadow-sm p-4 max-h-[400px] overflow-y-auto custom-scrollbar\">\n            <h3 className=\"text-lg font-bold text-[var(--color-dark-blue)] mb-4\">Table of Contents</h3>\n            <nav className=\"space-y-1\">\n              {toc.map((item) => {\n                const isActive = activeSection === item.id;\n                return (\n                  <div key={item.id}>\n                    <a\n                      href={`#${item.id}`}\n                      onClick={(e) => handleTocClick(e, item.id)}\n                      className={`block py-2 px-3 rounded-lg transition-all duration-200 text-sm font-medium ${\n                        isActive\n                          ? 'text-[var(--color-blue)] bg-[var(--color-blue)]/10'\n                          : 'text-gray-600 hover:text-[var(--color-dark-blue)] hover:bg-gray-100'\n                      }`}\n                    >\n                      {item.text}\n                    </a>\n                    {/* Sub-items (h2) */}\n                    {item.subItems && item.subItems.length > 0 && (\n                      <div className=\"ml-4 mt-1 space-y-1\">\n                        {item.subItems.map((subItem) => {\n                          const isSubActive = activeSection === subItem.id;\n                          return (\n                            <div key={subItem.id}>\n                              <a\n                                href={`#${subItem.id}`}\n                                onClick={(e) => handleTocClick(e, subItem.id)}\n                                className={`block py-1 px-2 rounded-lg transition-all duration-200 text-xs font-medium ${\n                                  isSubActive\n                                    ? 'text-[var(--color-blue)] bg-[var(--color-blue)]/10'\n                                    : 'text-gray-500 hover:text-[var(--color-dark-blue)] hover:bg-gray-100'\n                                }`}\n                              >\n                                {subItem.text}\n                              </a>\n                              {/* Sub-sub-items (h3) */}\n                              {subItem.subItems && subItem.subItems.length > 0 && (\n                                <div className=\"ml-4 mt-1 space-y-1\">\n                                  {subItem.subItems.map((subSubItem) => {\n                                    const isSubSubActive = activeSection === subSubItem.id;\n                                    return (\n                                      <a\n                                        key={subSubItem.id}\n                                        href={`#${subSubItem.id}`}\n                                        onClick={(e) => handleTocClick(e, subSubItem.id)}\n                                        className={`block py-1 px-2 rounded-lg transition-all duration-200 text-xs font-medium ${\n                                          isSubSubActive\n                                            ? 'text-[var(--color-blue)] bg-[var(--color-blue)]/10'\n                                            : 'text-gray-400 hover:text-[var(--color-dark-blue)] hover:bg-gray-100'\n                                        }`}\n                                      >\n                                        {subSubItem.text}\n                                      </a>\n                                    );\n                                  })}\n                                </div>\n                              )}\n                            </div>\n                          );\n                        })}\n                      </div>\n                    )}\n                  </div>\n                );\n              })}\n            </nav>\n          </div>\n        )}\n        \n        {/* Blog content for mobile */}\n        <div className=\"flex-1 min-w-0\">\n          <ContentSection toc={toc} onTocGenerated={handleTocGenerated}>{children}</ContentSection>\n        </div>\n        \n        {/* Image, Author, and Related Articles below blog content */}\n        {imageUrl && (\n          <div className=\"w-full aspect-[4/3] rounded-xl overflow-hidden shadow-lg bg-white border border-[var(--color-yellow)] flex items-center justify-center\">\n            <Image\n              src={imageUrl}\n              alt={title || \"Blog post featured image\"}\n              className=\"object-contain w-full h-full\"\n              width={340}\n              height={255}\n              priority\n            />\n          </div>\n        )}\n        {author && (\n          <div className=\"bg-gray-50 rounded-2xl p-6 border border-[var(--color-yellow)]\">\n            <h3 className=\"text-lg font-semibold text-[var(--color-dark-blue)] mb-4\">About the Author</h3>\n            <div className=\"flex items-center gap-3 mb-3\">\n              <div className=\"w-12 h-12 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-dark-blue)] rounded-full flex items-center justify-center\">\n                <span className=\"text-white font-bold text-xl\">{author.initials || author.name[0]}</span>\n              </div>\n              <div>\n                <p className=\"font-semibold text-[var(--color-dark-blue)]\">{author.name}</p>\n                <p className=\"text-sm text-[var(--foreground-secondary)]\">{author.role}</p>\n              </div>\n            </div>\n            <p className=\"text-sm text-[var(--foreground-secondary)]\">{author.bio}</p>\n          </div>\n        )}\n        {relatedArticles && relatedArticles.length > 0 && (\n          <div className=\"bg-gray-50 rounded-2xl p-6 border border-[var(--color-yellow)]\">\n            <h3 className=\"text-lg font-semibold text-[var(--color-dark-blue)] mb-4\">Related Articles</h3>\n            <div className=\"space-y-4\">\n              {relatedArticles.map((article, index) => (\n                <a\n                  key={index}\n                  href={article.link}\n                  className=\"block group\"\n                >\n                  <div className=\"flex gap-3\">\n                    <Image\n                      src={article.image}\n                      alt={article.title}\n                      width={60}\n                      height={40}\n                      className=\"rounded-lg object-cover border border-[var(--color-yellow)]\"\n                    />\n                    <div>\n                      <h4 className=\"text-sm font-semibold text-[var(--color-dark-blue)] group-hover:text-[var(--color-blue)] transition-colors line-clamp-2\">\n                        {article.title}\n                      </h4>\n                      <p className=\"text-xs text-[var(--foreground-secondary)]\">\n                        {article.date} • {article.readTime}\n                      </p>\n                    </div>\n                  </div>\n                </a>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n      \n      {/* Desktop Layout: Three columns */}\n      <div className=\"hidden lg:flex lg:flex-row gap-2 w-full max-w-7xl mx-auto bg-white\">\n        {/* Left Sidebar - TOC only */}\n        <aside className=\"hidden lg:block w-[250px] flex-shrink-0 pt-6 self-start\">\n          {toc && toc.length > 0 && (\n            <div\n              className={`bg-gray-50 rounded-xl shadow-sm p-6 max-h-[calc(100vh-8rem)] overflow-y-auto custom-scrollbar z-10 transition-all duration-300 ${\n                isSticky ? 'fixed' : 'relative'\n              }`}\n              style={isSticky ? {\n                top: `${tocPosition.top}px`,\n                left: `${tocPosition.left}px`,\n                width: '230px',\n                height: 'calc(100vh - 8rem)'\n              } : {\n                width: '230px',\n                height: 'auto'\n              }}\n            >\n              <h3 className=\"text-lg font-bold text-[var(--color-dark-blue)] mb-4\">Table of Contents</h3>\n              <nav className=\"space-y-1\">\n                {toc.map((item) => {\n                  const isActive = activeSection === item.id;\n                  return (\n                    <div key={item.id}>\n                      <a\n                        href={`#${item.id}`}\n                        onClick={(e) => {\n                          handleTocClick(e, item.id);\n                        }}\n                        className={`block py-2 px-3 rounded-lg transition-all duration-200 text-sm font-medium ${\n                          isActive\n                            ? 'text-[var(--color-blue)] bg-[var(--color-blue)]/10'\n                            : 'text-gray-600 hover:text-[var(--color-dark-blue)] hover:bg-gray-100'\n                        }`}\n                      >\n                        {item.text}\n                      </a>\n                      {/* Sub-items (h2) */}\n                      {item.subItems && item.subItems.length > 0 && (\n                        <div className=\"ml-4 mt-1 space-y-1\">\n                          {item.subItems.map((subItem) => {\n                            const isSubActive = activeSection === subItem.id;\n                            return (\n                              <div key={subItem.id}>\n                                <a\n                                  href={`#${subItem.id}`}\n                                  onClick={(e) => {\n                                    handleTocClick(e, subItem.id);\n                                  }}\n                                  className={`block py-1 px-2 rounded-lg transition-all duration-200 text-xs font-medium ${\n                                    isSubActive\n                                      ? 'text-[var(--color-blue)] bg-[var(--color-blue)]/10'\n                                      : 'text-gray-500 hover:text-[var(--color-dark-blue)] hover:bg-gray-100'\n                                  }`}\n                            >\n                              {subItem.text}\n                            </a>\n                                {/* Sub-sub-items (h3) */}\n                                {subItem.subItems && subItem.subItems.length > 0 && (\n                                  <div className=\"ml-4 mt-1 space-y-1\">\n                                    {subItem.subItems.map((subSubItem) => {\n                                      const isSubSubActive = activeSection === subSubItem.id;\n                                      return (\n                                        <a\n                                          key={subSubItem.id}\n                                          href={`#${subSubItem.id}`}\n                                          onClick={(e) => {\n                                            handleTocClick(e, subSubItem.id);\n                                          }}\n                                          className={`block py-1 px-2 rounded-lg transition-all duration-200 text-xs font-medium ${\n                                            isSubSubActive\n                                              ? 'text-[var(--color-blue)] bg-[var(--color-blue)]/10'\n                                              : 'text-gray-400 hover:text-[var(--color-dark-blue)] hover:bg-gray-100'\n                                          }`}\n                                        >\n                                          {subSubItem.text}\n                                        </a>\n                                      );\n                                    })}\n                                  </div>\n                                )}\n                              </div>\n                            );\n                          })}\n                        </div>\n                      )}\n                    </div>\n                  );\n                })}\n              </nav>\n            </div>\n          )}\n        </aside>\n        \n        {/* Main Content - Middle */}\n        <div className=\"flex-1 min-w-0\">\n          <ContentSection toc={toc} onTocGenerated={handleTocGenerated}>{children}</ContentSection>\n        </div>\n        \n        {/* Right Sidebar - Image, Author, Related Articles */}\n        <aside className=\"hidden lg:flex flex-col w-[250px] flex-shrink-0 pt-6 gap-4\">\n          {/* Image at the top */}\n          {imageUrl && (\n            <div className=\"w-full aspect-[4/3] rounded-xl overflow-hidden shadow-lg bg-white border border-[var(--color-yellow)] flex items-center justify-center\">\n              <Image\n                src={imageUrl}\n                alt={title || \"Blog post featured image\"}\n                className=\"object-contain w-full h-full\"\n                width={230}\n                height={172}\n                priority\n              />\n            </div>\n          )}\n          {/* Author Info (optional, if provided) */}\n          {author && (\n            <div className=\"bg-gray-50 rounded-2xl p-6 border border-[var(--color-yellow)]\">\n              <h3 className=\"text-lg font-semibold text-[var(--color-dark-blue)] mb-4\">About the Author</h3>\n              <div className=\"flex items-center gap-3 mb-3\">\n                <div className=\"w-12 h-12 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-dark-blue)] rounded-full flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-xl\">{author.initials || author.name[0]}</span>\n                </div>\n                <div>\n                  <p className=\"font-semibold text-[var(--color-dark-blue)]\">{author.name}</p>\n                  <p className=\"text-sm text-[var(--foreground-secondary)]\">{author.role}</p>\n                </div>\n              </div>\n              <p className=\"text-sm text-[var(--foreground-secondary)]\">{author.bio}</p>\n            </div>\n          )}\n          {/* Related Articles (optional, if provided) */}\n          {relatedArticles && relatedArticles.length > 0 && (\n            <div className=\"bg-gray-50 rounded-2xl p-6 border border-[var(--color-yellow)]\">\n              <h3 className=\"text-lg font-semibold text-[var(--color-dark-blue)] mb-4\">Related Articles</h3>\n              <div className=\"space-y-4\">\n                {relatedArticles.map((article, index) => (\n                  <a\n                    key={index}\n                    href={article.link}\n                    className=\"block group\"\n                  >\n                    <div className=\"flex gap-3\">\n                      <Image\n                        src={article.image}\n                        alt={article.title}\n                        width={50}\n                        height={35}\n                        className=\"rounded-lg object-cover border border-[var(--color-yellow)]\"\n                      />\n                      <div>\n                        <h4 className=\"text-sm font-semibold text-[var(--color-dark-blue)] group-hover:text-[var(--color-blue)] transition-colors line-clamp-2\">\n                          {article.title}\n                        </h4>\n                        <p className=\"text-xs text-[var(--foreground-secondary)]\">\n                          {article.date} • {article.readTime}\n                        </p>\n                      </div>\n                    </div>\n                  </a>\n                ))}\n              </div>\n            </div>\n          )}\n        </aside>\n      </div>\n    </div>\n  );\n};\n\nexport default FullBlogView;"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AALA;;;;;;;AAMA,gEAAgE;AAEhE,MAAM,eAAe,CAAC,EAAE,aAAa,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,KAAK,SAAS,EAAE;IACvF,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,KAAK;QAAG,MAAM;IAAE;IAEjE,yEAAyE;IACzE,MAAM,MAAM,iBAAiB,MAAM,GAAG,IAAI,mBAAmB;IAE7D,qEAAqE;IACrE,MAAM,gBAAgB;IAEtB,mDAAmD;IACnD,MAAM,uBAAuB,CAAC;QAC5B,MAAM,WAAW,KAAK,KAAK,CAAC;QAC5B,MAAM,WAAW,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE;QAC9C,OAAO,SACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;IACV;IAEA,MAAM,QAAQ,aAAa,qBAAqB;IAEhD,uCAAuC;IACvC,MAAM,kBAAkB,eAAe;QACrC;YACE,MAAM;YACN,KAAK;YACL,SAAS;YACT,aAAa;QACf;QACA;YACE,MAAM;YACN,KAAK;YACL,SAAS;YACT,aAAa;QACf;QACA;YACE,MAAM;YACN,KAAK;YACL,SAAS;YACT,aAAa,GAAG,MAAM,iEAAiE,CAAC;QAC1F;KACD;IAED,gDAAgD;IAChD,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,eAAe,EAAE,GAAG,iBAAiB,CAAC;IAEhE,2DAA2D;IAC3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,OAAO,IAAI,MAAM,KAAK,GAAG;QAE9B,MAAM,WAAW,IAAI,qBACnB,CAAC;YACC,QAAQ,OAAO,CAAC,CAAC;gBACf,IAAI,MAAM,cAAc,EAAE;oBACxB,iBAAiB,MAAM,MAAM,CAAC,EAAE;gBAClC;YACF;QACF,GACA;YAAE,YAAY,CAAC,CAAC,EAAE,cAAc,eAAe,CAAC;YAAE,WAAW;QAAE;QAGjE,mCAAmC;QACnC,MAAM,aAAa,CAAC;YAClB,IAAI,MAAM,EAAE;YACZ,MAAM,OAAO,CAAC,CAAA;gBACZ,IAAI,IAAI,CAAC,KAAK,EAAE;gBAChB,IAAI,KAAK,QAAQ,EAAE;oBACjB,MAAM,IAAI,MAAM,CAAC,WAAW,KAAK,QAAQ;gBAC3C;YACF;YACA,OAAO;QACT;QAEA,MAAM,SAAS,WAAW;QAE1B,OAAO,OAAO,CAAC,CAAC;YACd,MAAM,UAAU,SAAS,cAAc,CAAC;YACxC,IAAI,SAAS;gBACX,SAAS,OAAO,CAAC;YACnB;QACF;QAEA,OAAO,IAAM,SAAS,UAAU;IAClC,GAAG;QAAC;QAAK;KAAc;IAEvB,6BAA6B;IAC7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,MAAM,UAAU,OAAO,OAAO;YAC9B,MAAM,eAAe,OAAO,WAAW;YACvC,MAAM,iBAAiB,SAAS,eAAe,CAAC,YAAY;YAE5D,6BAA6B;YAC7B,4DAA4D;YAC5D,6DAA6D;YAC7D,MAAM,iBAAiB,UAAU,OAAO,AAAC,iBAAiB,UAAU,eAAgB;YAEpF,YAAY;YAEZ,IAAI,gBAAgB;gBAClB,yCAAyC;gBACzC,MAAM,gBAAgB,OAAO,UAAU;gBACvC,MAAM,kBAAkB,MAAM,6BAA6B;gBAC3D,MAAM,eAAe,KAAK,GAAG,CAAC,IAAI,CAAC,gBAAgB,eAAe,IAAI;gBACtE,eAAe;oBACb,KAAK;oBACL,MAAM;gBACR;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,gBAAgB,CAAC,UAAU;QAClC,gBAAgB,gBAAgB;QAEhC,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;YACrC,OAAO,mBAAmB,CAAC,UAAU;QACvC;IACF,GAAG,EAAE;IAEL,6DAA6D;IAC7D,MAAM,qBAAqB,CAAC;QAC1B,oBAAoB;IACtB;IAEA,+CAA+C;IAC/C,MAAM,iBAAiB,CAAC,GAAG;QACzB,EAAE,cAAc;QAChB,kDAAkD;QAClD,WAAW;YACT,kCAAkC;YAClC,IAAI,gBAAgB,SAAS,cAAc,CAAC;YAC5C,wDAAwD;YACxD,IAAI,eAAe;gBACjB,MAAM,OAAO,cAAc,qBAAqB;gBAChD,2FAA2F;gBAC3F,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,KAAK,GAAG;oBACzC,gBAAgB,MAAM,mCAAmC;gBAC3D;YACF;YACA,kEAAkE;YAClE,IAAI,CAAC,eAAe;gBAClB,+DAA+D;gBAC/D,MAAM,YAAY,OAAO,UAAU,IAAI;gBACvC,IAAI,WAAW;oBACb,4DAA4D;oBAC5D,MAAM,cAAc,SAAS,gBAAgB,CAAC;oBAC9C,MAAM,aAAa,SAAS,OAAO,CAAC,MAAM;oBAC1C,KAAK,IAAI,WAAW,YAAa;wBAC/B,MAAM,OAAO,QAAQ,qBAAqB;wBAC1C,MAAM,cAAc,QAAQ,WAAW,EAAE,iBAAiB;wBAC1D,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,MAAM,GAAG,KAChC,CAAC,QAAQ,EAAE,KAAK,YAAY,YAAY,QAAQ,CAAC,WAAW,WAAW,GAAG,GAAG;4BAC/E,gBAAgB;4BAChB;wBACF;oBACF;gBACF,OAAO;oBACL,oCAAoC;oBACpC,MAAM,cAAc,SAAS,gBAAgB,CAAC;oBAC9C,MAAM,aAAa,SAAS,OAAO,CAAC,MAAM;oBAC1C,KAAK,IAAI,WAAW,YAAa;wBAC/B,MAAM,cAAc,QAAQ,WAAW,EAAE,iBAAiB;wBAC1D,IAAI,YAAY,QAAQ,CAAC,WAAW,WAAW,KAAK;4BAClD,gBAAgB;4BAChB;wBACF;oBACF;gBACF;YACF;YACA,IAAI,eAAe;gBACjB,sCAAsC;gBACtC,MAAM,YAAY,OAAO,UAAU,IAAI,MAAM,gBAAgB;gBAC7D,6DAA6D;gBAC7D,IAAI,mBAAmB,cAAc,aAAa;gBAClD,MAAO,iBAAkB;oBACvB,MAAM,QAAQ,OAAO,gBAAgB,CAAC;oBACtC,IAAI,MAAM,QAAQ,KAAK,UAAU,MAAM,QAAQ,KAAK,YAAY,MAAM,SAAS,KAAK,UAAU,MAAM,SAAS,KAAK,UAAU;wBAC1H;oBACF;oBACA,mBAAmB,iBAAiB,aAAa;gBACnD;gBACA,IAAI,WAAW;oBACb,oDAAoD;oBACpD,cAAc,cAAc,CAAC;wBAC3B,UAAU;wBACV,OAAO;wBACP,QAAQ;oBACV;oBACA,WAAW;wBACT,MAAM,YAAY,cAAc,qBAAqB;wBACrD,+CAA+C;wBAC/C,IAAI,UAAU,GAAG,IAAI,KAAK,UAAU,GAAG,IAAI,OAAO,WAAW,EAAE;4BAC7D,MAAM,aAAa,UAAU,GAAG,GAAG,KAAK,8BAA8B;4BACtE,IAAI,KAAK,GAAG,CAAC,cAAc,IAAI;gCAC7B,OAAO,QAAQ,CAAC;oCACd,KAAK;oCACL,UAAU;gCACZ;4BACF;wBACF;oBACF,GAAG;gBACL,OAAO;oBACL,MAAM,SAAS;oBACf,MAAM,OAAO,cAAc,qBAAqB;oBAChD,MAAM,iBAAiB,OAAO,WAAW;oBACzC,MAAM,qBAAqB,KAAK,GAAG,GAAG;oBACtC,MAAM,uBAAuB,qBAAqB;oBAClD,OAAO,QAAQ,CAAC;wBACd,KAAK;wBACL,UAAU;oBACZ;gBACF;gBACA,WAAW;oBACT,MAAM,YAAY,cAAc,qBAAqB;oBACrD,iEAAiE;oBACjE,IAAI,UAAU,GAAG,GAAG,OAAO,WAAW,GAAG,OAAO,UAAU,GAAG,GAAG,GAAG;wBACjE,cAAc,cAAc,CAAC;4BAC3B,UAAU;4BACV,OAAO;wBACT;wBACA,WAAW;4BACT,IAAI,WAAW;gCACb,OAAO,QAAQ,CAAC;oCACd,KAAK,CAAC;oCACN,UAAU;gCACZ;4BACF;wBACF,GAAG;oBACL;gBACF,GAAG;YACL,OAAO;gBACL,wDAAwD;gBACxD,MAAM,cAAc,SAAS,aAAa,CAAC;gBAC3C,IAAI,aAAa;oBACf,YAAY,cAAc,CAAC;wBACzB,UAAU;wBACV,OAAO;oBACT;gBACF;YACF;QACF,GAAG,KAAK,qCAAqC;IAC/C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,+JAAA,CAAA,UAAa;gBACX,GAAG,aAAa;gBACjB,OAAO;gBACP,aAAa;gBACb,KAAK;;;;;;0BAIP,8OAAC;gBAAI,WAAU;;oBAEZ,OAAO,IAAI,MAAM,GAAG,mBACnB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAuD;;;;;;0CACrE,8OAAC;gCAAI,WAAU;0CACZ,IAAI,GAAG,CAAC,CAAC;oCACR,MAAM,WAAW,kBAAkB,KAAK,EAAE;oCAC1C,qBACE,8OAAC;;0DACC,8OAAC;gDACC,MAAM,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;gDACnB,SAAS,CAAC,IAAM,eAAe,GAAG,KAAK,EAAE;gDACzC,WAAW,CAAC,2EAA2E,EACrF,WACI,uDACA,uEACJ;0DAED,KAAK,IAAI;;;;;;4CAGX,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,mBACvC,8OAAC;gDAAI,WAAU;0DACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC;oDAClB,MAAM,cAAc,kBAAkB,QAAQ,EAAE;oDAChD,qBACE,8OAAC;;0EACC,8OAAC;gEACC,MAAM,CAAC,CAAC,EAAE,QAAQ,EAAE,EAAE;gEACtB,SAAS,CAAC,IAAM,eAAe,GAAG,QAAQ,EAAE;gEAC5C,WAAW,CAAC,2EAA2E,EACrF,cACI,uDACA,uEACJ;0EAED,QAAQ,IAAI;;;;;;4DAGd,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,mBAC7C,8OAAC;gEAAI,WAAU;0EACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC;oEACrB,MAAM,iBAAiB,kBAAkB,WAAW,EAAE;oEACtD,qBACE,8OAAC;wEAEC,MAAM,CAAC,CAAC,EAAE,WAAW,EAAE,EAAE;wEACzB,SAAS,CAAC,IAAM,eAAe,GAAG,WAAW,EAAE;wEAC/C,WAAW,CAAC,2EAA2E,EACrF,iBACI,uDACA,uEACJ;kFAED,WAAW,IAAI;uEATX,WAAW,EAAE;;;;;gEAYxB;;;;;;;uDA/BI,QAAQ,EAAE;;;;;gDAoCxB;;;;;;;uCAtDI,KAAK,EAAE;;;;;gCA2DrB;;;;;;;;;;;;kCAMN,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gKAAA,CAAA,UAAc;4BAAC,KAAK;4BAAK,gBAAgB;sCAAqB;;;;;;;;;;;oBAIhE,0BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAK;4BACL,KAAK,SAAS;4BACd,WAAU;4BACV,OAAO;4BACP,QAAQ;4BACR,QAAQ;;;;;;;;;;;oBAIb,wBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2D;;;;;;0CACzE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAgC,OAAO,QAAQ,IAAI,OAAO,IAAI,CAAC,EAAE;;;;;;;;;;;kDAEnF,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAA+C,OAAO,IAAI;;;;;;0DACvE,8OAAC;gDAAE,WAAU;0DAA8C,OAAO,IAAI;;;;;;;;;;;;;;;;;;0CAG1E,8OAAC;gCAAE,WAAU;0CAA8C,OAAO,GAAG;;;;;;;;;;;;oBAGxE,mBAAmB,gBAAgB,MAAM,GAAG,mBAC3C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2D;;;;;;0CACzE,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC,SAAS,sBAC7B,8OAAC;wCAEC,MAAM,QAAQ,IAAI;wCAClB,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK,QAAQ,KAAK;oDAClB,KAAK,QAAQ,KAAK;oDAClB,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;8DAEZ,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEACX,QAAQ,KAAK;;;;;;sEAEhB,8OAAC;4DAAE,WAAU;;gEACV,QAAQ,IAAI;gEAAC;gEAAI,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;;uCAjBnC;;;;;;;;;;;;;;;;;;;;;;0BA6BjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAM,WAAU;kCACd,OAAO,IAAI,MAAM,GAAG,mBACnB,8OAAC;4BACC,WAAW,CAAC,+HAA+H,EACzI,WAAW,UAAU,YACrB;4BACF,OAAO,WAAW;gCAChB,KAAK,GAAG,YAAY,GAAG,CAAC,EAAE,CAAC;gCAC3B,MAAM,GAAG,YAAY,IAAI,CAAC,EAAE,CAAC;gCAC7B,OAAO;gCACP,QAAQ;4BACV,IAAI;gCACF,OAAO;gCACP,QAAQ;4BACV;;8CAEA,8OAAC;oCAAG,WAAU;8CAAuD;;;;;;8CACrE,8OAAC;oCAAI,WAAU;8CACZ,IAAI,GAAG,CAAC,CAAC;wCACR,MAAM,WAAW,kBAAkB,KAAK,EAAE;wCAC1C,qBACE,8OAAC;;8DACC,8OAAC;oDACC,MAAM,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;oDACnB,SAAS,CAAC;wDACR,eAAe,GAAG,KAAK,EAAE;oDAC3B;oDACA,WAAW,CAAC,2EAA2E,EACrF,WACI,uDACA,uEACJ;8DAED,KAAK,IAAI;;;;;;gDAGX,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,mBACvC,8OAAC;oDAAI,WAAU;8DACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC;wDAClB,MAAM,cAAc,kBAAkB,QAAQ,EAAE;wDAChD,qBACE,8OAAC;;8EACC,8OAAC;oEACC,MAAM,CAAC,CAAC,EAAE,QAAQ,EAAE,EAAE;oEACtB,SAAS,CAAC;wEACR,eAAe,GAAG,QAAQ,EAAE;oEAC9B;oEACA,WAAW,CAAC,2EAA2E,EACrF,cACI,uDACA,uEACJ;8EAEL,QAAQ,IAAI;;;;;;gEAGV,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,mBAC7C,8OAAC;oEAAI,WAAU;8EACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC;wEACrB,MAAM,iBAAiB,kBAAkB,WAAW,EAAE;wEACtD,qBACE,8OAAC;4EAEC,MAAM,CAAC,CAAC,EAAE,WAAW,EAAE,EAAE;4EACzB,SAAS,CAAC;gFACR,eAAe,GAAG,WAAW,EAAE;4EACjC;4EACA,WAAW,CAAC,2EAA2E,EACrF,iBACI,uDACA,uEACJ;sFAED,WAAW,IAAI;2EAXX,WAAW,EAAE;;;;;oEAcxB;;;;;;;2DAnCI,QAAQ,EAAE;;;;;oDAwCxB;;;;;;;2CA5DI,KAAK,EAAE;;;;;oCAiErB;;;;;;;;;;;;;;;;;kCAOR,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gKAAA,CAAA,UAAc;4BAAC,KAAK;4BAAK,gBAAgB;sCAAqB;;;;;;;;;;;kCAIjE,8OAAC;wBAAM,WAAU;;4BAEd,0BACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAK;oCACL,KAAK,SAAS;oCACd,WAAU;oCACV,OAAO;oCACP,QAAQ;oCACR,QAAQ;;;;;;;;;;;4BAKb,wBACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2D;;;;;;kDACzE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAgC,OAAO,QAAQ,IAAI,OAAO,IAAI,CAAC,EAAE;;;;;;;;;;;0DAEnF,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAA+C,OAAO,IAAI;;;;;;kEACvE,8OAAC;wDAAE,WAAU;kEAA8C,OAAO,IAAI;;;;;;;;;;;;;;;;;;kDAG1E,8OAAC;wCAAE,WAAU;kDAA8C,OAAO,GAAG;;;;;;;;;;;;4BAIxE,mBAAmB,gBAAgB,MAAM,GAAG,mBAC3C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2D;;;;;;kDACzE,8OAAC;wCAAI,WAAU;kDACZ,gBAAgB,GAAG,CAAC,CAAC,SAAS,sBAC7B,8OAAC;gDAEC,MAAM,QAAQ,IAAI;gDAClB,WAAU;0DAEV,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,6HAAA,CAAA,UAAK;4DACJ,KAAK,QAAQ,KAAK;4DAClB,KAAK,QAAQ,KAAK;4DAClB,OAAO;4DACP,QAAQ;4DACR,WAAU;;;;;;sEAEZ,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EACX,QAAQ,KAAK;;;;;;8EAEhB,8OAAC;oEAAE,WAAU;;wEACV,QAAQ,IAAI;wEAAC;wEAAI,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;;+CAjBnC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BzB;uCAEe", "debugId": null}}, {"offset": {"line": 1249, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1256, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/next/src/shared/lib/image-external.tsx"], "sourcesContent": ["import type { ImageConfigComplete, ImageLoaderProps } from './image-config'\nimport type { ImageProps, ImageLoader, StaticImageData } from './get-img-props'\n\nimport { getImgProps } from './get-img-props'\nimport { Image } from '../../client/image-component'\n\n// This is replaced by webpack alias\nimport defaultLoader from 'next/dist/shared/lib/image-loader'\n\n/**\n * For more advanced use cases, you can call `getImageProps()`\n * to get the props that would be passed to the underlying `<img>` element,\n * and instead pass to them to another component, style, canvas, etc.\n *\n * Read more: [Next.js docs: `getImageProps`](https://nextjs.org/docs/app/api-reference/components/image#getimageprops)\n */\nexport function getImageProps(imgProps: ImageProps) {\n  const { props } = getImgProps(imgProps, {\n    defaultLoader,\n    // This is replaced by webpack define plugin\n    imgConf: process.env.__NEXT_IMAGE_OPTS as any as ImageConfigComplete,\n  })\n  // Normally we don't care about undefined props because we pass to JSX,\n  // but this exported function could be used by the end user for anything\n  // so we delete undefined props to clean it up a little.\n  for (const [key, value] of Object.entries(props)) {\n    if (value === undefined) {\n      delete props[key as keyof typeof props]\n    }\n  }\n  return { props }\n}\n\nexport default Image\n\nexport type { ImageProps, ImageLoaderProps, ImageLoader, StaticImageData }\n"], "names": ["getImageProps", "imgProps", "props", "getImgProps", "defaultLoader", "imgConf", "process", "env", "__NEXT_IMAGE_OPTS", "key", "value", "Object", "entries", "undefined", "Image"], "mappings": ";;;;;;;;;;;;;;IAiCA,OAAoB,EAAA;eAApB;;IAjBgBA,aAAa,EAAA;eAAbA;;;;6BAbY;gCACN;sEAGI;AASnB,SAASA,cAAcC,QAAoB;IAChD,MAAM,EAAEC,KAAK,EAAE,GAAGC,CAAAA,GAAAA,aAAAA,WAAW,EAACF,UAAU;QACtCG,eAAAA,aAAAA,OAAa;QACb,4CAA4C;QAC5CC,OAAAA,EAASC,QAAQC,GAAG,CAACC,iBAAiB;IACxC;IACA,uEAAuE;IACvE,wEAAwE;IACxE,wDAAwD;IACxD,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACV,OAAQ;QAChD,IAAIQ,UAAUG,WAAW;YACvB,OAAOX,KAAK,CAACO,IAA0B;QACzC;IACF;IACA,OAAO;QAAEP;IAAM;AACjB;MAEA,WAAeY,gBAAAA,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1305, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/next/image.js"], "sourcesContent": ["module.exports = require('./dist/shared/lib/image-external')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}