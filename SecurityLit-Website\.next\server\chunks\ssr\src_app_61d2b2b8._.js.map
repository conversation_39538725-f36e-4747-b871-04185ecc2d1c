{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/common/components/BreadcrumbNavigation.jsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from 'react';\r\nimport Link from 'next/link';\r\nimport { ChevronRight, Home, FileText, Users, Settings, MapPin, Shield, Code, Smartphone, Globe, DollarSign, Building, Phone, Download } from 'lucide-react';\r\nimport { motion } from 'framer-motion';\r\n\r\n/**\r\n * Truncated Text Component for Mobile Breadcrumbs\r\n * Shows truncated text on mobile with click to expand functionality\r\n */\r\nconst TruncatedText = ({ text, maxLength = 25, onExpansionChange }) => {\r\n  const [isExpanded, setIsExpanded] = useState(false);\r\n\r\n  const handleToggle = () => {\r\n    const newExpanded = !isExpanded;\r\n    setIsExpanded(newExpanded);\r\n    if (onExpansionChange) {\r\n      onExpansionChange(newExpanded);\r\n    }\r\n  };\r\n\r\n  if (text.length <= maxLength) {\r\n    return <span>{text}</span>;\r\n  }\r\n\r\n  return (\r\n    <span\r\n      className=\"cursor-pointer\"\r\n      onClick={handleToggle}\r\n      title={isExpanded ? \"Click to collapse\" : \"Click to expand\"}\r\n    >\r\n      <span className=\"sm:hidden\">\r\n        {isExpanded ? text : `${text.substring(0, maxLength)}...`}\r\n      </span>\r\n      <span className=\"hidden sm:inline\">\r\n        {text}\r\n      </span>\r\n    </span>\r\n  );\r\n};\r\n\r\n/**\r\n * Structured Data Component for SEO\r\n */\r\nconst BreadcrumbStructuredData = ({ items }) => {\r\n  const structuredData = {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"BreadcrumbList\",\r\n    \"itemListElement\": items.map((item, index) => ({\r\n      \"@type\": \"ListItem\",\r\n      \"position\": index + 1,\r\n      \"name\": item.name,\r\n      \"item\": item.current ? undefined : `https://securitylit.com${item.url}`\r\n    }))\r\n  };\r\n\r\n  return (\r\n    <script\r\n      type=\"application/ld+json\"\r\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}\r\n    />\r\n  );\r\n};\r\n\r\n/**\r\n * Universal Breadcrumb Navigation Component with SEO structured data\r\n * Provides clear navigation hierarchy and improves SEO across all pages\r\n */\r\nconst BreadcrumbNavigation = ({ items, className = \"\" }) => {\r\n  const [hasExpandedText, setHasExpandedText] = useState(false);\r\n\r\n  // Check if this is a blog page for conditional padding\r\n  const isBlogPage = items?.some(item =>\r\n    item.url?.includes('/Blogs') ||\r\n    item.iconKey === 'blogs' ||\r\n    className?.includes('blog')\r\n  );\r\n\r\n  return (\r\n    <>\r\n      {/* Structured Data for SEO */}\r\n      <BreadcrumbStructuredData items={items} />\r\n      \r\n      {/* Simple Breadcrumb Navigation */}\r\n      <nav\r\n        className={`bg-transparent py-2 sm:py-3 ${\r\n          isBlogPage\r\n            ? hasExpandedText\r\n              ? 'pb-6 sm:pb-4'\r\n              : 'pb-3 sm:pb-4'\r\n            : 'pb-2 sm:pb-3'\r\n        } ${className}`}\r\n        aria-label=\"Breadcrumb navigation\"\r\n      >\r\n        <div className=\"max-w-7xl mx-auto px-4\">\r\n          <motion.ol\r\n            className={`flex items-center space-x-1 text-xs sm:text-sm ${\r\n              className?.includes('text-white')\r\n                ? 'text-white'\r\n                : className?.includes('text-blue-600')\r\n                ? 'text-blue-600'\r\n                : 'text-gray-600'\r\n            }`}\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            transition={{ duration: 0.4 }}\r\n          >\r\n            {items.map((item, index) => (\r\n              <motion.li\r\n                key={index}\r\n                className=\"flex items-center\"\r\n                initial={{ opacity: 0 }}\r\n                animate={{ opacity: 1 }}\r\n                transition={{ duration: 0.3, delay: index * 0.1 }}\r\n              >\r\n                {index > 0 && (\r\n                  <ChevronRight\r\n                    className={`w-3 h-3 mx-1 ${\r\n                      className?.includes('text-white')\r\n                        ? 'text-white/70'\r\n                        : className?.includes('text-blue-600')\r\n                        ? 'text-blue-400'\r\n                        : 'text-gray-400'\r\n                    }`}\r\n                    aria-hidden=\"true\"\r\n                  />\r\n                )}\r\n\r\n                {item.current ? (\r\n                  <span\r\n                    className={`font-medium flex items-center gap-1 ${\r\n                      className?.includes('text-white')\r\n                        ? 'text-white'\r\n                        : className?.includes('text-blue-600')\r\n                        ? 'text-blue-600'\r\n                        : 'text-gray-900'\r\n                    }`}\r\n                    aria-current=\"page\"\r\n                    title={item.description}\r\n                  >\r\n                    {item.iconKey === 'home' && (\r\n                      <Home className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'blogs' && (\r\n                      <FileText className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'contact-us' && (\r\n                      <Phone className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                    <TruncatedText\r\n                      text={item.name}\r\n                      onExpansionChange={setHasExpandedText}\r\n                    />\r\n                  </span>\r\n                ) : (\r\n                  <Link\r\n                    href={item.url}\r\n                    className={`transition-colors duration-200 flex items-center gap-1 hover:underline ${\r\n                      className?.includes('text-white')\r\n                        ? 'text-white hover:text-white/80'\r\n                        : className?.includes('text-blue-600')\r\n                        ? 'text-blue-600 hover:text-blue-800'\r\n                        : 'text-blue-600 hover:text-blue-800'\r\n                    }`}\r\n                    title={item.description}\r\n                  >\r\n                    {item.iconKey === 'home' && (\r\n                      <Home className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-current'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'blogs' && (\r\n                      <FileText className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-current'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'contact-us' && (\r\n                      <Phone className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-current'\r\n                      }`} />\r\n                    )}\r\n                    <TruncatedText\r\n                      text={item.name}\r\n                      onExpansionChange={setHasExpandedText}\r\n                    />\r\n                  </Link>\r\n                )}\r\n              </motion.li>\r\n            ))}\r\n          </motion.ol>\r\n        </div>\r\n      </nav>\r\n    </>\r\n  );\r\n};\r\n\r\n// Client-side helper function for generating breadcrumb items\r\nexport const generateBreadcrumbs = (pageType, params = {}) => {\r\n  const baseBreadcrumb = {\r\n    name: \"Home\",\r\n    url: \"/\",\r\n    iconKey: \"home\",\r\n    description: \"Return to homepage\"\r\n  };\r\n\r\n  const breadcrumbConfigs = {\r\n    blog: (slug) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Blog\",\r\n        url: \"/Blogs\",\r\n        iconKey: \"blogs\",\r\n        description: \"View all blog posts\"\r\n      },\r\n      {\r\n        name: params.title || \"Blog Post\",\r\n        url: `/Blogs/${slug}`,\r\n        current: true,\r\n        description: `${params.title || 'Blog post'} - Cybersecurity insights and penetration testing guidance`\r\n      }\r\n    ],\r\n    \r\n    service: (service) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Services\",\r\n        url: \"/Services\",\r\n        iconKey: \"services\",\r\n        description: \"View all cybersecurity services\"\r\n      },\r\n      {\r\n        name: params.title || service,\r\n        url: `/Services/${service}`,\r\n        current: true,\r\n        iconKey: service,\r\n        description: `${params.title || service} penetration testing services`\r\n      }\r\n    ],\r\n\r\n    location: (country) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Locations\",\r\n        url: \"/Locations\",\r\n        iconKey: \"locations\",\r\n        description: \"View all locations\"\r\n      },\r\n      {\r\n        name: params.countryName || country,\r\n        url: `/Locations/${country}`,\r\n        current: true,\r\n        description: `${params.countryName || country} cybersecurity services`\r\n      }\r\n    ],\r\n\r\n    company: (page) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Company\",\r\n        url: \"/Company\",\r\n        iconKey: \"company\",\r\n        description: \"Learn about SecurityLit\"\r\n      },\r\n      {\r\n        name: params.title || page,\r\n        url: `/Company/${page}`,\r\n        current: true,\r\n        description: params.description || `${page} information`\r\n      }\r\n    ],\r\n\r\n    'company-size': (size) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Company Size\",\r\n        url: \"/Company-size\",\r\n        iconKey: \"company\",\r\n        description: \"Solutions for different company sizes\"\r\n      },\r\n      {\r\n        name: params.title || size,\r\n        url: `/Company-size/${size}`,\r\n        current: true,\r\n        description: params.description || `${size} cybersecurity solutions`\r\n      }\r\n    ],\r\n\r\n    industry: (industry) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Industries\",\r\n        url: \"/Industries\",\r\n        iconKey: \"company\",\r\n        description: \"Industry-specific cybersecurity solutions\"\r\n      },\r\n      {\r\n        name: params.title || industry,\r\n        url: `/Industries/${industry}`,\r\n        current: true,\r\n        description: params.description || `${industry} cybersecurity solutions`\r\n      }\r\n    ],\r\n\r\n    product: (product) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Product\",\r\n        url: \"/Product\",\r\n        iconKey: \"product\",\r\n        description: \"Explore our cybersecurity products\"\r\n      },\r\n      {\r\n        name: params.title || product,\r\n        url: `/Product/${product}`,\r\n        current: true,\r\n        description: params.description || `${product} product information`\r\n      }\r\n    ],\r\n\r\n    simple: (pageName, url) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: pageName,\r\n        url: url,\r\n        current: true,\r\n        iconKey: params.iconKey,\r\n        description: params.description || `${pageName} page`\r\n      }\r\n    ]\r\n  };\r\n\r\n  return breadcrumbConfigs[pageType] || (() => [baseBreadcrumb]);\r\n};\r\n\r\nexport default BreadcrumbNavigation; "], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;;AAMA;;;CAGC,GACD,MAAM,gBAAgB,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,EAAE,iBAAiB,EAAE;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,eAAe;QACnB,MAAM,cAAc,CAAC;QACrB,cAAc;QACd,IAAI,mBAAmB;YACrB,kBAAkB;QACpB;IACF;IAEA,IAAI,KAAK,MAAM,IAAI,WAAW;QAC5B,qBAAO,8OAAC;sBAAM;;;;;;IAChB;IAEA,qBACE,8OAAC;QACC,WAAU;QACV,SAAS;QACT,OAAO,aAAa,sBAAsB;;0BAE1C,8OAAC;gBAAK,WAAU;0BACb,aAAa,OAAO,GAAG,KAAK,SAAS,CAAC,GAAG,WAAW,GAAG,CAAC;;;;;;0BAE3D,8OAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAIT;AAEA;;CAEC,GACD,MAAM,2BAA2B,CAAC,EAAE,KAAK,EAAE;IACzC,MAAM,iBAAiB;QACrB,YAAY;QACZ,SAAS;QACT,mBAAmB,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAC7C,SAAS;gBACT,YAAY,QAAQ;gBACpB,QAAQ,KAAK,IAAI;gBACjB,QAAQ,KAAK,OAAO,GAAG,YAAY,CAAC,uBAAuB,EAAE,KAAK,GAAG,EAAE;YACzE,CAAC;IACH;IAEA,qBACE,8OAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAgB;;;;;;AAGxE;AAEA;;;CAGC,GACD,MAAM,uBAAuB,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,uDAAuD;IACvD,MAAM,aAAa,OAAO,KAAK,CAAA,OAC7B,KAAK,GAAG,EAAE,SAAS,aACnB,KAAK,OAAO,KAAK,WACjB,WAAW,SAAS;IAGtB,qBACE;;0BAEE,8OAAC;gBAAyB,OAAO;;;;;;0BAGjC,8OAAC;gBACC,WAAW,CAAC,4BAA4B,EACtC,aACI,kBACE,iBACA,iBACF,eACL,CAAC,EAAE,WAAW;gBACf,cAAW;0BAEX,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wBACR,WAAW,CAAC,+CAA+C,EACzD,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;wBACF,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,UAAU;wBAAI;kCAE3B,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCAER,WAAU;gCACV,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;;oCAE/C,QAAQ,mBACP,8OAAC,sNAAA,CAAA,eAAY;wCACX,WAAW,CAAC,aAAa,EACvB,WAAW,SAAS,gBAChB,kBACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;wCACF,eAAY;;;;;;oCAIf,KAAK,OAAO,iBACX,8OAAC;wCACC,WAAW,CAAC,oCAAoC,EAC9C,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;wCACF,gBAAa;wCACb,OAAO,KAAK,WAAW;;4CAEtB,KAAK,OAAO,KAAK,wBAChB,8OAAC,mMAAA,CAAA,OAAI;gDAAC,WAAW,CAAC,QAAQ,EACxB,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;;;;;;4CAEH,KAAK,OAAO,KAAK,yBAChB,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAW,CAAC,QAAQ,EAC5B,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;;;;;;4CAEH,KAAK,OAAO,KAAK,8BAChB,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAW,CAAC,QAAQ,EACzB,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;;;;;;0DAEJ,8OAAC;gDACC,MAAM,KAAK,IAAI;gDACf,mBAAmB;;;;;;;;;;;iGAIvB,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,GAAG;wCACd,WAAW,CAAC,uEAAuE,EACjF,WAAW,SAAS,gBAChB,mCACA,WAAW,SAAS,mBACpB,sCACA,qCACJ;wCACF,OAAO,KAAK,WAAW;;4CAEtB,KAAK,OAAO,KAAK,wBAChB,8OAAC,mMAAA,CAAA,OAAI;gDAAC,WAAW,CAAC,QAAQ,EACxB,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,gBACJ;;;;;;4CAEH,KAAK,OAAO,KAAK,yBAChB,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAW,CAAC,QAAQ,EAC5B,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,gBACJ;;;;;;4CAEH,KAAK,OAAO,KAAK,8BAChB,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAW,CAAC,QAAQ,EACzB,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,gBACJ;;;;;;0DAEJ,8OAAC;gDACC,MAAM,KAAK,IAAI;gDACf,mBAAmB;;;;;;;;;;;;;+BAxGpB;;;;;;;;;;;;;;;;;;;;;;AAmHrB;AAGO,MAAM,sBAAsB,CAAC,UAAU,SAAS,CAAC,CAAC;IACvD,MAAM,iBAAiB;QACrB,MAAM;QACN,KAAK;QACL,SAAS;QACT,aAAa;IACf;IAEA,MAAM,oBAAoB;QACxB,MAAM,CAAC,OAAS;gBACd;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,OAAO,EAAE,MAAM;oBACrB,SAAS;oBACT,aAAa,GAAG,OAAO,KAAK,IAAI,YAAY,0DAA0D,CAAC;gBACzG;aACD;QAED,SAAS,CAAC,UAAY;gBACpB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,UAAU,EAAE,SAAS;oBAC3B,SAAS;oBACT,SAAS;oBACT,aAAa,GAAG,OAAO,KAAK,IAAI,QAAQ,6BAA6B,CAAC;gBACxE;aACD;QAED,UAAU,CAAC,UAAY;gBACrB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,WAAW,IAAI;oBAC5B,KAAK,CAAC,WAAW,EAAE,SAAS;oBAC5B,SAAS;oBACT,aAAa,GAAG,OAAO,WAAW,IAAI,QAAQ,uBAAuB,CAAC;gBACxE;aACD;QAED,SAAS,CAAC,OAAS;gBACjB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,SAAS,EAAE,MAAM;oBACvB,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,GAAG,KAAK,YAAY,CAAC;gBAC1D;aACD;QAED,gBAAgB,CAAC,OAAS;gBACxB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,cAAc,EAAE,MAAM;oBAC5B,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,GAAG,KAAK,wBAAwB,CAAC;gBACtE;aACD;QAED,UAAU,CAAC,WAAa;gBACtB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,YAAY,EAAE,UAAU;oBAC9B,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,GAAG,SAAS,wBAAwB,CAAC;gBAC1E;aACD;QAED,SAAS,CAAC,UAAY;gBACpB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,SAAS,EAAE,SAAS;oBAC1B,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,GAAG,QAAQ,oBAAoB,CAAC;gBACrE;aACD;QAED,QAAQ,CAAC,UAAU,MAAQ;gBACzB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,SAAS,OAAO,OAAO;oBACvB,aAAa,OAAO,WAAW,IAAI,GAAG,SAAS,KAAK,CAAC;gBACvD;aACD;IACH;IAEA,OAAO,iBAAiB,CAAC,SAAS,IAAI,CAAC,IAAM;YAAC;SAAe;AAC/D;uCAEe", "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/privacy/components/privacyPolicy.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport BreadcrumbNavigation from \"../../common/components/BreadcrumbNavigation\";\r\n\r\nexport default function PrivacyPolicy() {\r\n  const [activeSection, setActiveSection] = useState(\"Introduction\");\r\n\r\n  const breadcrumbItems = [\r\n    { name: \"Home\", url: \"/\", iconKey: \"home\" },\r\n    { name: \"Privacy Policy\", url: \"/privacy\", current: true, iconKey: \"company\" }\r\n  ];\r\n\r\n  const sections = [\r\n    \"Introduction\",\r\n    \"Changes to this Policy\",\r\n    \"Collection of Personal Information\",\r\n    \"Use of Personal Information\",\r\n    \"Disclosing your Personal Information\",\r\n    \"Protecting your Personal Information\",\r\n    \"Accessing and Correcting your Personal Information\",\r\n    \"Destruction of your information\",\r\n    \"Third party cookies and other technologies\"\r\n  ];\r\n\r\n  // This offset should match the scroll-margin-top utility (e.g., scroll-mt-32)\r\n  // to account for your fixed navbar. 128px = 8rem = pt-32/scroll-mt-32\r\n  const SCROLL_OFFSET = 128; \r\n\r\n  useEffect(() => {\r\n    const observer = new IntersectionObserver(\r\n      (entries) => {\r\n        entries.forEach((entry) => {\r\n          if (entry.isIntersecting) {\r\n            setActiveSection(entry.target.getAttribute(\"data-section\"));\r\n          }\r\n        });\r\n      },\r\n      // rootMargin defines a \"trigger line\" at the top of the viewport\r\n      { rootMargin: `-${SCROLL_OFFSET}px 0px -80% 0px`, threshold: 0 }\r\n    );\r\n\r\n    sections.forEach((section) => {\r\n      const element = document.getElementById(section.toLowerCase().replace(/\\s+/g, '-'));\r\n      if (element) {\r\n        element.setAttribute('data-section', section);\r\n        observer.observe(element);\r\n      }\r\n    });\r\n\r\n    return () => observer.disconnect();\r\n  }, [sections]);\r\n\r\n  const handleTocClick = (e, section) => {\r\n    e.preventDefault();\r\n    setActiveSection(section.name); // Instantly update active state\r\n\r\n    const element = document.getElementById(section.id);\r\n    if (element) {\r\n      const elementPosition = element.getBoundingClientRect().top;\r\n      const offsetPosition = elementPosition + window.pageYOffset - SCROLL_OFFSET;\r\n\r\n      window.scrollTo({\r\n        top: offsetPosition,\r\n        behavior: \"smooth\"\r\n      });\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-white\">\r\n      {/* Hero Section with Dark Background */}\r\n      <section className=\"bg-[var(--color-dark-blue)] py-20 lg:py-28\">\r\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\r\n          <h1 className=\"text-4xl lg:text-5xl font-bold text-white mb-4\">\r\n            Privacy Policy\r\n          </h1>\r\n          <p className=\"text-lg lg:text-xl text-white/80 max-w-2xl mx-auto\">\r\n            How we collect, use, and protect your personal information.\r\n          </p>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Content Section */}\r\n      <section className=\"py-16 lg:py-20\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"mb-12\">\r\n            <BreadcrumbNavigation items={breadcrumbItems} />\r\n          </div>\r\n          \r\n          <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8 lg:gap-12\">\r\n            \r\n            {/* Left Content */}\r\n            <div className=\"lg:col-span-3 prose prose-lg max-w-none\">\r\n              \r\n              <div id=\"introduction\" className=\"mb-12 scroll-mt-32\">\r\n                <h2 className=\"text-2xl font-bold text-[var(--color-dark-blue)] mb-6\">Introduction</h2>\r\n                <p>Security Lit Ltd. complies with the New Zealand Privacy Act 1993 when dealing with personal information. This policy sets out how we will collect, use, disclose and protect your personal information. This policy does not limit or exclude any of your rights under the Act. If you wish to seek further information on the Act, see <a href=\"https://www.privacy.org.nz\" className=\"text-[var(--color-blue)] hover:underline\" target=\"_blank\" rel=\"noopener noreferrer\">www.privacy.org.nz</a>.</p>\r\n              </div>\r\n\r\n              <div id=\"changes-to-this-policy\" className=\"mb-12 scroll-mt-32\">\r\n                <h2 className=\"text-2xl font-bold text-[var(--color-dark-blue)] mb-6\">Changes to this Policy</h2>\r\n                <p>We reserve the right to make updates and changes this policy from time to time. If we do revise this privacy policy, we will notify you either by making site announcements that are visible the next time you visit the website or we will send an email notification to you.</p>\r\n              </div>\r\n              \r\n              <div id=\"collection-of-personal-information\" className=\"mb-12 scroll-mt-32\">\r\n                <h2 className=\"text-2xl font-bold text-[var(--color-dark-blue)] mb-6\">Collection of Personal Information</h2>\r\n                <p>We may collect personal information directly or indirectly through your use of this website and its services or functions, through any contact with us (e.g. telephone call or email), or when you use our services. We may also collect it from third parties where you have authorised this or the information is publicly available.</p>\r\n              </div>\r\n\r\n              <div id=\"use-of-personal-information\" className=\"mb-12 scroll-mt-32\">\r\n                <h2 className=\"text-2xl font-bold text-[var(--color-dark-blue)] mb-6\">Use of Personal Information</h2>\r\n                <p>Any personal information we receive will be used primarily to respond to inquiries, to sell, supply and deliver services which you order, to provide you with news and information about us and products, services and activities that we believe you may be interested in, to respond to complaints, to improve this website and our products and services and for such other purposes as may be disclosed to you at the time the personal information is collected or as required by law.</p>\r\n              </div>\r\n\r\n              <div id=\"disclosing-your-personal-information\" className=\"mb-12 scroll-mt-32\">\r\n                <h2 className=\"text-2xl font-bold text-[var(--color-dark-blue)] mb-6\">Disclosing your Personal Information</h2>\r\n                <p>We may disclose your personal information to:</p>\r\n                <ul className=\"list-disc list-inside space-y-2 ml-4\">\r\n                  <li>Any business that supports our services and products, including any person that hosts or maintains any underlying IT system or data centre that we use to provide the website or other services and products.</li>\r\n                  <li>A credit reference agency for the purpose of credit checking you.</li>\r\n                  <li>Any person if required to do so by law.</li>\r\n                  <li>Any other person authorised by you.</li>\r\n                </ul>\r\n              </div>\r\n\r\n              <div id=\"protecting-your-personal-information\" className=\"mb-12 scroll-mt-32\">\r\n                <h2 className=\"text-2xl font-bold text-[var(--color-dark-blue)] mb-6\">Protecting your Personal Information</h2>\r\n                <p>We will take all reasonable steps to ensure that the personal information we collect, use, or disclose is accurate, complete, up to date and stored in a secure environment protected from unauthorised access, modification or disclosure.</p>\r\n              </div>\r\n\r\n              <div id=\"accessing-and-correcting-your-personal-information\" className=\"mb-12 scroll-mt-32\">\r\n                <h2 className=\"text-2xl font-bold text-[var(--color-dark-blue)] mb-6\">Accessing and Correcting your Personal Information</h2>\r\n                <p>You may access and request correction of personal information that we hold about you by contacting us. We will deal with requests for access to and correction of personal information as quickly as possible.</p>\r\n              </div>\r\n\r\n              <div id=\"destruction-of-your-information\" className=\"mb-12 scroll-mt-32\">\r\n                <h2 className=\"text-2xl font-bold text-[var(--color-dark-blue)] mb-6\">Destruction of your information</h2>\r\n                <p>We take all reasonable steps to ensure your personal information is appropriately disposed of once it is no longer needed for the purpose for which it was collected.</p>\r\n              </div>\r\n\r\n              <div id=\"third-party-cookies-and-other-technologies\" className=\"mb-12 scroll-mt-32\">\r\n                <h2 className=\"text-2xl font-bold text-[var(--color-dark-blue)] mb-6\">Third party cookies and other technologies</h2>\r\n                <p>We use third party cookies and other technologies for marketing and to gather website analytics. This includes:</p>\r\n                <ul className=\"list-disc list-inside space-y-2 ml-4\">\r\n                  <li><strong>Remarketing and emails:</strong> we use third party cookies – to keep track of the services you are interested in.</li>\r\n                  <li><strong>Impression reporting:</strong> we use web beacons to estimate the number of users that have viewed and clicked on our website. (as a result, we are able to gauge the success of a campaign).</li>\r\n                </ul>\r\n              </div>\r\n\r\n              {/* Contact Information Box */}\r\n              <div className=\"bg-gray-50 rounded-xl p-8 mt-16 not-prose\">\r\n                <h2 className=\"text-2xl font-bold text-[var(--color-dark-blue)] mb-6\">Contact Us</h2>\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 text-base\">\r\n                  <div>\r\n                    <h3 className=\"text-lg font-semibold text-[var(--color-dark-blue)] mb-3\">Our Address</h3>\r\n                    <p><strong>New Zealand - Waikato, Hamilton</strong><br /><strong>India - 5th Floor, DLF Two Horizon Centre, DLF Phase 5, Gurugram</strong></p>\r\n                  </div>\r\n                  <div>\r\n                    <h3 className=\"text-lg font-semibold text-[var(--color-dark-blue)] mb-3\">Contact Information</h3>\r\n                    <p><strong>Email:</strong> <a href=\"mailto:<EMAIL>\" className=\"text-[var(--color-blue)] hover:underline\"><EMAIL></a><br /><strong>New Zealand:</strong> +64 (03) 394 0821<br /><strong>India:</strong> +91 8527 800769</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Right Sidebar - Table of Contents */}\r\n            <div className=\"lg:col-span-1\">\r\n              <div className=\"lg:sticky top-32 max-h-[calc(100vh-10rem)] overflow-y-auto bg-gray-50 rounded-xl shadow-sm p-4 lg:p-6\">\r\n                <h3 className=\"text-lg font-bold text-[var(--color-dark-blue)] mb-4\">Table of Contents</h3>\r\n                <nav className=\"space-y-1\">\r\n                  {sections.map((sectionName) => {\r\n                    const sectionId = sectionName.toLowerCase().replace(/\\s+/g, '-');\r\n                    const isActive = activeSection === sectionName;\r\n                    \r\n                    return (\r\n                      <a\r\n                        key={sectionName}\r\n                        href={`#${sectionId}`}\r\n                        className={`block py-2 px-3 rounded-lg transition-all duration-200 text-sm font-medium ${\r\n                          isActive\r\n                            ? 'text-[var(--color-blue)] bg-[var(--color-blue)]/10'\r\n                            : 'text-gray-600 hover:text-[var(--color-dark-blue)] hover:bg-gray-100'\r\n                        }`}\r\n                        onClick={(e) => handleTocClick(e, { name: sectionName, id: sectionId })}\r\n                      >\r\n                        {sectionName}\r\n                      </a>\r\n                    );\r\n                  })}\r\n                </nav>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,kBAAkB;QACtB;YAAE,MAAM;YAAQ,KAAK;YAAK,SAAS;QAAO;QAC1C;YAAE,MAAM;YAAkB,KAAK;YAAY,SAAS;YAAM,SAAS;QAAU;KAC9E;IAED,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,8EAA8E;IAC9E,sEAAsE;IACtE,MAAM,gBAAgB;IAEtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,IAAI,qBACnB,CAAC;YACC,QAAQ,OAAO,CAAC,CAAC;gBACf,IAAI,MAAM,cAAc,EAAE;oBACxB,iBAAiB,MAAM,MAAM,CAAC,YAAY,CAAC;gBAC7C;YACF;QACF,GACA,iEAAiE;QACjE;YAAE,YAAY,CAAC,CAAC,EAAE,cAAc,eAAe,CAAC;YAAE,WAAW;QAAE;QAGjE,SAAS,OAAO,CAAC,CAAC;YAChB,MAAM,UAAU,SAAS,cAAc,CAAC,QAAQ,WAAW,GAAG,OAAO,CAAC,QAAQ;YAC9E,IAAI,SAAS;gBACX,QAAQ,YAAY,CAAC,gBAAgB;gBACrC,SAAS,OAAO,CAAC;YACnB;QACF;QAEA,OAAO,IAAM,SAAS,UAAU;IAClC,GAAG;QAAC;KAAS;IAEb,MAAM,iBAAiB,CAAC,GAAG;QACzB,EAAE,cAAc;QAChB,iBAAiB,QAAQ,IAAI,GAAG,gCAAgC;QAEhE,MAAM,UAAU,SAAS,cAAc,CAAC,QAAQ,EAAE;QAClD,IAAI,SAAS;YACX,MAAM,kBAAkB,QAAQ,qBAAqB,GAAG,GAAG;YAC3D,MAAM,iBAAiB,kBAAkB,OAAO,WAAW,GAAG;YAE9D,OAAO,QAAQ,CAAC;gBACd,KAAK;gBACL,UAAU;YACZ;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAiD;;;;;;sCAG/D,8OAAC;4BAAE,WAAU;sCAAqD;;;;;;;;;;;;;;;;;0BAOtE,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,2JAAA,CAAA,UAAoB;gCAAC,OAAO;;;;;;;;;;;sCAG/B,8OAAC;4BAAI,WAAU;;8CAGb,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,IAAG;4CAAe,WAAU;;8DAC/B,8OAAC;oDAAG,WAAU;8DAAwD;;;;;;8DACtE,8OAAC;;wDAAE;sEAAwU,8OAAC;4DAAE,MAAK;4DAA6B,WAAU;4DAA2C,QAAO;4DAAS,KAAI;sEAAsB;;;;;;wDAAsB;;;;;;;;;;;;;sDAGve,8OAAC;4CAAI,IAAG;4CAAyB,WAAU;;8DACzC,8OAAC;oDAAG,WAAU;8DAAwD;;;;;;8DACtE,8OAAC;8DAAE;;;;;;;;;;;;sDAGL,8OAAC;4CAAI,IAAG;4CAAqC,WAAU;;8DACrD,8OAAC;oDAAG,WAAU;8DAAwD;;;;;;8DACtE,8OAAC;8DAAE;;;;;;;;;;;;sDAGL,8OAAC;4CAAI,IAAG;4CAA8B,WAAU;;8DAC9C,8OAAC;oDAAG,WAAU;8DAAwD;;;;;;8DACtE,8OAAC;8DAAE;;;;;;;;;;;;sDAGL,8OAAC;4CAAI,IAAG;4CAAuC,WAAU;;8DACvD,8OAAC;oDAAG,WAAU;8DAAwD;;;;;;8DACtE,8OAAC;8DAAE;;;;;;8DACH,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;;;;;;;;;;;;;sDAIR,8OAAC;4CAAI,IAAG;4CAAuC,WAAU;;8DACvD,8OAAC;oDAAG,WAAU;8DAAwD;;;;;;8DACtE,8OAAC;8DAAE;;;;;;;;;;;;sDAGL,8OAAC;4CAAI,IAAG;4CAAqD,WAAU;;8DACrE,8OAAC;oDAAG,WAAU;8DAAwD;;;;;;8DACtE,8OAAC;8DAAE;;;;;;;;;;;;sDAGL,8OAAC;4CAAI,IAAG;4CAAkC,WAAU;;8DAClD,8OAAC;oDAAG,WAAU;8DAAwD;;;;;;8DACtE,8OAAC;8DAAE;;;;;;;;;;;;sDAGL,8OAAC;4CAAI,IAAG;4CAA6C,WAAU;;8DAC7D,8OAAC;oDAAG,WAAU;8DAAwD;;;;;;8DACtE,8OAAC;8DAAE;;;;;;8DACH,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;;8EAAG,8OAAC;8EAAO;;;;;;gEAAgC;;;;;;;sEAC5C,8OAAC;;8EAAG,8OAAC;8EAAO;;;;;;gEAA8B;;;;;;;;;;;;;;;;;;;sDAK9C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAwD;;;;;;8DACtE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAA2D;;;;;;8EACzE,8OAAC;;sFAAE,8OAAC;sFAAO;;;;;;sFAAwC,8OAAC;;;;;sFAAK,8OAAC;sFAAO;;;;;;;;;;;;;;;;;;sEAEnE,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAA2D;;;;;;8EACzE,8OAAC;;sFAAE,8OAAC;sFAAO;;;;;;wEAAe;sFAAC,8OAAC;4EAAE,MAAK;4EAAiC,WAAU;sFAA2C;;;;;;sFAA2B,8OAAC;;;;;sFAAK,8OAAC;sFAAO;;;;;;wEAAqB;sFAAkB,8OAAC;;;;;sFAAK,8OAAC;sFAAO;;;;;;wEAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAO9O,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAuD;;;;;;0DACrE,8OAAC;gDAAI,WAAU;0DACZ,SAAS,GAAG,CAAC,CAAC;oDACb,MAAM,YAAY,YAAY,WAAW,GAAG,OAAO,CAAC,QAAQ;oDAC5D,MAAM,WAAW,kBAAkB;oDAEnC,qBACE,8OAAC;wDAEC,MAAM,CAAC,CAAC,EAAE,WAAW;wDACrB,WAAW,CAAC,2EAA2E,EACrF,WACI,uDACA,uEACJ;wDACF,SAAS,CAAC,IAAM,eAAe,GAAG;gEAAE,MAAM;gEAAa,IAAI;4DAAU;kEAEpE;uDATI;;;;;gDAYX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlB", "debugId": null}}]}