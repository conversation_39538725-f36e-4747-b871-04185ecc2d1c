{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/Home/components/heroSection.jsx"], "sourcesContent": ["// src/components/Hero.jsx (or your existing file path)\r\n\"use client\";\r\n\r\nimport React, { useState } from 'react';\r\nimport { PrimaryButton, SecondaryButton, OutlinedButton } from \"@/app/common/buttons/BrandButtons\";\r\nimport { FaArrowRight, FaShieldAlt, FaCheckCircle, FaLock } from 'react-icons/fa';\r\n\r\n// Reusable component for the benefit list items\r\nconst BenefitItem = ({ text }) => (\r\n  <li className=\"flex items-center gap-3\">\r\n    <FaCheckCircle className=\"text-green-500 h-5 w-5 flex-shrink-0\" />\r\n    <span className=\"text-white/80\">{text}</span>\r\n  </li>\r\n);\r\n\r\nexport default function Hero() {\r\n  const [formData, setFormData] = useState({ name: '', email: '' });\r\n  const locations = ['Gurugram', 'Noida', 'Mumbai', 'Bangalore', 'Delhi', 'Pune', 'Hyderabad', 'Chennai'];\r\n\r\n  const handleInputChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData(prev => ({ ...prev, [name]: value }));\r\n  };\r\n\r\n  const handleSubmit = (e) => {\r\n    e.preventDefault();\r\n    console.log(\"Form Submitted:\", formData);\r\n    // Add your form submission logic here\r\n  };\r\n\r\n  return (\r\n    <section className=\"relative w-full min-h-screen flex items-center pt-16 md:pt-20 bg-[var(--color-dark-blue)]\" style={{\r\n      backgroundImage: 'url(/images/bg-image.png)',\r\n      backgroundSize: 'cover',\r\n      backgroundPosition: 'center right',\r\n      backgroundRepeat: 'no-repeat'\r\n    }}>\r\n      {/* Background Grid */}\r\n      {/* <div className=\"absolute inset-0 cyber-grid opacity-20 z-0\"></div> */}\r\n\r\n      {/* Overlay for better text readability */}\r\n      <div className=\"absolute inset-0 bg-[var(--color-dark-blue)]/80 z-0\"></div>\r\n\r\n      <div className=\"w-full max-w-7xl mx-auto px-2 sm:px-4 lg:px-0 relative z-10\">\r\n        <div className=\"grid lg:grid-cols-2 gap-8 lg:gap-16 items-center max-w-full\">\r\n          \r\n          {/* Left Column: Content */}\r\n          <div className=\"space-y-6 md:space-y-8 w-full fade-in-up\">\r\n            <div className=\"inline-flex items-center gap-2 px-3 py-1.5 md:px-4 md:py-2 bg-gray-900/80 border border-white/20 rounded-full text-xs md:text-sm font-semibold text-[var(--color-yellow)]\">\r\n              <FaShieldAlt className=\"text-[var(--color-yellow)] text-xs md:text-sm\" />\r\n              <span>Trusted by Fortune 500</span>\r\n            </div>\r\n\r\n            <h1 className=\"text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-tight\">\r\n              Secure Your<br />\r\n              <span style={{ color: 'var(--color-blue)' }}>Digital Future</span>\r\n            </h1>\r\n\r\n            <p className=\"text-base md:text-lg text-white/80 max-w-xl\">\r\n              Comprehensive cybersecurity consulting services for businesses across India. From vCISO services to red teaming, we protect what matters most.\r\n            </p>\r\n\r\n            <div className=\"flex flex-wrap items-center gap-2 md:gap-3\">\r\n              <span className=\"text-xs md:text-sm font-medium text-white/60\">Serving:</span>\r\n              {locations.slice(0, 4).map((loc, index) => (\r\n                <span \r\n                  key={loc} \r\n                  className=\"px-2 py-1 md:px-3 md:py-1 bg-white/10 border border-white/20 text-white rounded-full text-xs md:text-sm cursor-pointer\"\r\n                  style={{ animationDelay: `${index * 0.1}s` }}\r\n                >\r\n                  {loc}\r\n                </span>\r\n              ))}\r\n              <span className=\"px-2 py-1 md:px-3 md:py-1 bg-[var(--color-blue)] text-white rounded-full text-xs md:text-sm font-semibold\">\r\n                + more\r\n              </span>\r\n            </div>\r\n\r\n            <div className=\"flex flex-col sm:flex-row gap-3 md:gap-4 pt-2 md:pt-4\">\r\n              <PrimaryButton className=\"px-4 py-2.5 md:px-5 md:py-2.5 text-sm md:text-base\">\r\n                Talk to Our Expert <FaArrowRight />\r\n              </PrimaryButton>\r\n              <button className=\"px-4 py-2.5 md:px-5 md:py-2.5 text-sm md:text-base bg-[var(--color-yellow)] text-[var(--color-gray)] border-2 border-[var(--color-yellow)] rounded-lg font-bold transition-all duration-300 hover:bg-white hover:text-[var(--color-dark-blue)] hover:border-white shadow-md flex items-center justify-center gap-2 cursor-pointer\">\r\n                Our Services\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Right Column: Empty for spacing */}\r\n          <div className=\"hidden lg:block relative\">\r\n            {/* Content can be added here if needed */}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,uDAAuD;;;;;AAGvD;AACA;AACA;AAJA;;;;;AAMA,gDAAgD;AAChD,MAAM,cAAc,CAAC,EAAE,IAAI,EAAE,iBAC3B,8OAAC;QAAG,WAAU;;0BACZ,8OAAC,8IAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;0BACzB,8OAAC;gBAAK,WAAU;0BAAiB;;;;;;;;;;;;AAItB,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,MAAM;QAAI,OAAO;IAAG;IAC/D,MAAM,YAAY;QAAC;QAAY;QAAS;QAAU;QAAa;QAAS;QAAQ;QAAa;KAAU;IAEvG,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;IACjD;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,QAAQ,GAAG,CAAC,mBAAmB;IAC/B,sCAAsC;IACxC;IAEA,qBACE,8OAAC;QAAQ,WAAU;QAA4F,OAAO;YACpH,iBAAiB;YACjB,gBAAgB;YAChB,oBAAoB;YACpB,kBAAkB;QACpB;;0BAKE,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAGb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,8IAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC;sDAAK;;;;;;;;;;;;8CAGR,8OAAC;oCAAG,WAAU;;wCAAkF;sDACnF,8OAAC;;;;;sDACZ,8OAAC;4CAAK,OAAO;gDAAE,OAAO;4CAAoB;sDAAG;;;;;;;;;;;;8CAG/C,8OAAC;oCAAE,WAAU;8CAA8C;;;;;;8CAI3D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAA+C;;;;;;wCAC9D,UAAU,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC/B,8OAAC;gDAEC,WAAU;gDACV,OAAO;oDAAE,gBAAgB,GAAG,QAAQ,IAAI,CAAC,CAAC;gDAAC;0DAE1C;+CAJI;;;;;sDAOT,8OAAC;4CAAK,WAAU;sDAA4G;;;;;;;;;;;;8CAK9H,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gJAAA,CAAA,gBAAa;4CAAC,WAAU;;gDAAqD;8DACzD,8OAAC,8IAAA,CAAA,eAAY;;;;;;;;;;;sDAElC,8OAAC;4CAAO,WAAU;sDAAoU;;;;;;;;;;;;;;;;;;sCAO1V,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAOzB", "debugId": null}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/Home/components/OurServices.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport Image from \"next/image\";\r\nimport { AccentButton } from \"@/app/common/buttons/BrandButtons\";\r\nimport { Shield, Target, Eye, Network, AlertTriangle, FileCheck } from \"lucide-react\";\r\n\r\nexport function AnimatedPinDemo() {\r\n  const services = [\r\n    {\r\n      title: \"V CISO as a Service\",\r\n      description: \"Expert cybersecurity leadership and strategy, on-demand.\",\r\n      features: [\r\n        \"Strategic security planning & roadmaps\",\r\n        \"Risk management & threat assessment\", \r\n        \"Compliance guidance & oversight\"\r\n      ],\r\n      icon: <Shield className=\"w-12 h-12 text-blue-300\" strokeWidth={1.5} />\r\n    },\r\n    {\r\n      title: \"Red Teaming\",\r\n      description: \"Simulated cyberattacks to test and strengthen your defenses.\",\r\n      features: [\r\n        \"Realistic attack simulations\",\r\n        \"Vulnerability identification & exploitation\",\r\n        \"Post-breach analysis & recommendations\"\r\n      ],\r\n      icon: <Target className=\"w-12 h-12 text-red-300\" strokeWidth={1.5} />\r\n    },\r\n    {\r\n      title: \"VAPT Services\",\r\n      description: \"Find and fix security gaps in your systems and apps.\",\r\n      features: [\r\n        \"Comprehensive vulnerability scanning\",\r\n        \"Manual penetration testing\",\r\n        \"Detailed reports & remediation guidance\"\r\n      ],\r\n      icon: <Eye className=\"w-12 h-12 text-green-300\" strokeWidth={1.5} />\r\n    },\r\n    {\r\n      title: \"Web3 Audit\",\r\n      description: \"Comprehensive security audits for blockchain and web3 projects.\",\r\n      features: [\r\n        \"Smart contract security audits\",\r\n        \"Blockchain protocol review\",\r\n        \"Decentralized application (dApp) security\"\r\n      ],\r\n      icon: <Network className=\"w-12 h-12 text-purple-300\" strokeWidth={1.5} />\r\n    },\r\n    {\r\n      title: \"Incident Response\",\r\n      description: \"Rapid response to security incidents with expert crisis management.\",\r\n      features: [\r\n        \"24/7 incident detection & analysis\",\r\n        \"Threat containment & eradication\",\r\n        \"Post-incident recovery & reporting\"\r\n      ],\r\n      icon: <AlertTriangle className=\"w-12 h-12 text-orange-300\" strokeWidth={1.5} />\r\n    },\r\n    {\r\n      title: \"Compliance Pre-Assessment\",\r\n      description: \"Get audit-ready with a quick compliance health check.\",\r\n      features: [\r\n        \"Regulatory compliance gap analysis\",\r\n        \"Policy & procedure review\",\r\n        \"Readiness for certifications (e.g., ISO, SOC 2)\"\r\n      ],\r\n      icon: <FileCheck className=\"w-12 h-12 text-cyan-300\" strokeWidth={1.5} />\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <section className=\"relative z-10 w-full px-2 sm:px-4 md:px-8 lg:px-16 py-8 md:py-12 lg:py-20 bg-white\">\r\n      <div className=\"max-w-7xl mx-auto\">\r\n        {/* Header Section */}\r\n        <div className=\"text-center mb-12 md:mb-16 pt-4 px-2\">\r\n          <h2 className=\"text-4xl md:text-5xl font-bold text-brand-light-blue mb-4 md:mb-6\">\r\n            Our Services\r\n          </h2>\r\n          <p className=\"text-base sm:text-lg md:text-xl text-brand-navy max-w-3xl md:max-w-5xl mx-auto leading-relaxed\">\r\n            We offer a comprehensive suite of cybersecurity services designed to protect your business, empower your team, and ensure compliance. Explore our core offerings below to see how we can help secure your digital future.\r\n          </p>\r\n        </div>\r\n\r\n        {/* Service Cards Grid */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8 pb-16\">\r\n          {services.map((service, index) => (\r\n            <div key={index} className=\"group\">\r\n              <div className=\"bg-brand-light-blue rounded-3xl p-8 h-full shadow-2xl hover:shadow-3xl transition-all duration-500 hover:-translate-y-2 border border-white/10 relative overflow-hidden\">\r\n                {/* Loading/Shimmer Effect */}\r\n                <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-in-out\"></div>\r\n                \r\n                {/* Header with Icon and Title */}\r\n                <div className=\"flex items-start gap-5 mb-6 relative z-10\">\r\n                  <div className=\"w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center flex-shrink-0 backdrop-blur-sm border border-white/20\">\r\n                    {service.icon}\r\n                  </div>\r\n                  <div className=\"flex-1 min-w-0\">\r\n                    <h3 className=\"text-2xl font-bold text-white mb-3 leading-tight\">\r\n                      {service.title}\r\n                    </h3>\r\n                    <p className=\"text-white/90 text-base leading-relaxed\">\r\n                      {service.description}\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Features List */}\r\n                <div className=\"mb-8 relative z-10\">\r\n                  <ul className=\"space-y-3\">\r\n                    {service.features.map((feature, featureIndex) => (\r\n                      <li key={featureIndex} className=\"flex items-start gap-3 text-white/95 text-sm\">\r\n                        <div className=\"w-2 h-2 bg-white/70 rounded-full mt-2.5 flex-shrink-0\"></div>\r\n                        <span className=\"leading-relaxed font-medium\">{feature}</span>\r\n                      </li>\r\n                    ))}\r\n                  </ul>\r\n                </div>\r\n\r\n                {/* Learn More Button */}\r\n                <div className=\"mt-auto relative z-10\">\r\n                  <AccentButton className=\"w-full justify-center text-base py-4 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300\">\r\n                    Learn More\r\n                    <svg \r\n                      className=\"w-5 h-5 ml-2 transition-transform duration-300 group-hover:translate-x-1\" \r\n                      fill=\"none\" \r\n                      stroke=\"currentColor\" \r\n                      viewBox=\"0 0 24 24\"\r\n                    >\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\r\n                    </svg>\r\n                  </AccentButton>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Many More Services Section */}\r\n        <div className=\"text-center mt-20 md:mt-24\">\r\n          <h3 className=\"text-3xl md:text-4xl font-bold text-brand-navy mb-4\">\r\n            And Many More Services\r\n          </h3>\r\n          <p className=\"text-lg text-brand-navy/70 max-w-2xl mx-auto\">\r\n            Explore our complete range of cybersecurity solutions designed to protect your business\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n\r\nexport default AnimatedPinDemo;\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAOO,SAAS;IACd,MAAM,WAAW;QACf;YACE,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;aACD;YACD,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;gBAA0B,aAAa;;;;;;QACjE;QACA;YACE,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;aACD;YACD,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;gBAAyB,aAAa;;;;;;QAChE;QACA;YACE,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;aACD;YACD,oBAAM,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;gBAA2B,aAAa;;;;;;QAC/D;QACA;YACE,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;aACD;YACD,oBAAM,8OAAC,wMAAA,CAAA,UAAO;gBAAC,WAAU;gBAA4B,aAAa;;;;;;QACpE;QACA;YACE,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;aACD;YACD,oBAAM,8OAAC,wNAAA,CAAA,gBAAa;gBAAC,WAAU;gBAA4B,aAAa;;;;;;QAC1E;QACA;YACE,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;aACD;YACD,oBAAM,8OAAC,gNAAA,CAAA,YAAS;gBAAC,WAAU;gBAA0B,aAAa;;;;;;QACpE;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoE;;;;;;sCAGlF,8OAAC;4BAAE,WAAU;sCAAiG;;;;;;;;;;;;8BAMhH,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;4BAAgB,WAAU;sCACzB,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;;;;;kDAGf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,IAAI;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,QAAQ,KAAK;;;;;;kEAEhB,8OAAC;wDAAE,WAAU;kEACV,QAAQ,WAAW;;;;;;;;;;;;;;;;;;kDAM1B,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAG,WAAU;sDACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC9B,8OAAC;oDAAsB,WAAU;;sEAC/B,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAK,WAAU;sEAA+B;;;;;;;mDAFxC;;;;;;;;;;;;;;;kDASf,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,gJAAA,CAAA,eAAY;4CAAC,WAAU;;gDAAyG;8DAE/H,8OAAC;oDACC,WAAU;oDACV,MAAK;oDACL,QAAO;oDACP,SAAQ;8DAER,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BA1CrE;;;;;;;;;;8BAoDd,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsD;;;;;;sCAGpE,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;;;;;;;;;;;;;;;;;;AAOtE;uCAEe", "debugId": null}}, {"offset": {"line": 597, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/Home/components/WhyChooseUsSection.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { Shield, Users, Target, Award, Clock, CheckCircle, ArrowRight } from \"lucide-react\";\r\n\r\n// Counter component for animated numbers\r\nconst Counter = ({ end, duration = 2000, suffix = \"\" }) => {\r\n  const [count, setCount] = useState(0);\r\n\r\n  useEffect(() => {\r\n    let startTime = null;\r\n    const startValue = 0;\r\n    const endValue = end;\r\n\r\n    const animate = (currentTime) => {\r\n      if (!startTime) startTime = currentTime;\r\n      const progress = Math.min((currentTime - startTime) / duration, 1);\r\n      \r\n      const currentCount = Math.floor(startValue + (endValue - startValue) * progress);\r\n      setCount(currentCount);\r\n\r\n      if (progress < 1) {\r\n        requestAnimationFrame(animate);\r\n      }\r\n    };\r\n\r\n    requestAnimationFrame(animate);\r\n  }, [end, duration]);\r\n\r\n  return <span>{count}{suffix}</span>;\r\n};\r\n\r\nexport function WhyChooseUsSection() {\r\n  const mainFeatures = [\r\n    {\r\n      icon: Shield,\r\n      number: \"01\",\r\n      title: \"Enterprise-Grade Security\",\r\n      subtitle: \"Complete Protection Suite\",\r\n      description: \"From Virtual CISO leadership to incident response, we deliver comprehensive cybersecurity solutions that scale with your business needs.\",\r\n      color: \"blue\"\r\n    },\r\n    {\r\n      icon: Target,\r\n      number: \"02\", \r\n      title: \"Proven Methodology\",\r\n      subtitle: \"Real-World Testing\",\r\n      description: \"Our red team exercises and penetration testing simulate sophisticated attacks, strengthening your defenses against emerging threats.\",\r\n      color: \"purple\"\r\n    },\r\n    {\r\n      icon: Users,\r\n      number: \"03\",\r\n      title: \"Expert Partnership\",\r\n      subtitle: \"15+ Years Experience\",\r\n      description: \"Recognized by industry leaders including Facebook, Google, and US DoD. We bring world-class expertise to every engagement.\",\r\n      color: \"green\"\r\n    }\r\n  ];\r\n\r\n  const trustIndicators = [\r\n    { metric: 200, label: \"Organizations Protected\", icon: Shield, suffix: \"+\" },\r\n    { metric: \"24/7\", label: \"Security Monitoring\", icon: Clock, suffix: \"\" },\r\n    { metric: 99.9, label: \"Uptime Guarantee\", icon: CheckCircle, suffix: \"%\" },\r\n    { metric: 0, label: \"Security Breaches\", icon: Award, suffix: \"\" }\r\n  ];\r\n\r\n  return (\r\n    <section className=\"py-24 bg-gray-50 relative overflow-hidden\">\r\n      {/* Subtle Background Pattern */}\r\n      <div className=\"absolute inset-0\">\r\n        <div className=\"absolute top-0 left-1/4 w-72 h-72 bg-blue-100/20 rounded-full blur-3xl\"></div>\r\n        <div className=\"absolute bottom-0 right-1/4 w-96 h-96 bg-purple-100/20 rounded-full blur-3xl\"></div>\r\n      </div>\r\n\r\n      <div className=\"w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\">\r\n        {/* Header Section */}\r\n        <div className=\"text-center mb-20\">\r\n          <div className=\"inline-flex items-center bg-[var(--color-blue)]/10 px-4 py-2 rounded-full mb-6\">\r\n            <div className=\"w-2 h-2 bg-[var(--color-blue)] rounded-full mr-2\"></div>\r\n            <span className=\"text-sm font-medium text-[var(--color-blue)]\">Why SecurityLit</span>\r\n          </div>\r\n          \r\n          <h2 className=\"text-5xl lg:text-6xl font-bold text-[var(--color-dark-blue)] mb-6 leading-tight\">\r\n            Your Trusted\r\n            <span className=\"block text-[var(--color-blue)]\">\r\n              Cybersecurity Partner\r\n            </span>\r\n          </h2>\r\n          \r\n          <p className=\"text-xl text-[var(--color-dark-blue)]/70 max-w-3xl mx-auto leading-relaxed\">\r\n            Protecting what matters most with comprehensive security solutions, expert guidance, and proven results across industries.\r\n          </p>\r\n        </div>\r\n\r\n        {/* Main Features Grid */}\r\n        <div className=\"grid lg:grid-cols-3 gap-8 mb-20\">\r\n          {mainFeatures.map((feature, index) => {\r\n            const IconComponent = feature.icon;\r\n            const colorVariants = {\r\n              blue: \"bg-[var(--color-blue)]\",\r\n              purple: \"bg-[var(--color-dark-blue)]\", \r\n              green: \"bg-[var(--color-yellow)]\"\r\n            };\r\n            \r\n            return (\r\n              <div key={index} className=\"group relative\">\r\n                {/* Card */}\r\n                <div className=\"bg-white rounded-2xl p-8 shadow-md border border-gray-100 border-t-4 border-t-[var(--color-blue)] hover:shadow-xl hover:-translate-y-2 transition-all duration-300 h-full\">\r\n                  {/* Number Badge */}\r\n                  <div className=\"flex items-center justify-between mb-6\">\r\n                    <span className=\"text-4xl font-bold text-gray-200 group-hover:text-gray-300 transition-colors\">\r\n                      {feature.number}\r\n                    </span>\r\n                    <div className={`w-14 h-14 ${colorVariants[feature.color]} rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform`}>\r\n                      <IconComponent className=\"w-7 h-7 text-white\" />\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Content */}\r\n                  <h3 className=\"text-2xl font-bold text-[var(--color-dark-blue)] mb-2\">\r\n                    {feature.title}\r\n                  </h3>\r\n                  <p className={`font-semibold mb-4 text-lg ${\r\n                    feature.color === 'blue' ? 'text-[var(--color-blue)]' :\r\n                    feature.color === 'purple' ? 'text-[var(--color-dark-blue)]' : 'text-[var(--color-yellow)]'\r\n                  }`}>\r\n                    {feature.subtitle}\r\n                  </p>\r\n                  <p className=\"text-[var(--color-dark-blue)]/70 leading-relaxed mb-6\">\r\n                    {feature.description}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n\r\n        {/* Trust Metrics */}\r\n        <div className=\"bg-[var(--color-dark-blue)] rounded-3xl p-12 text-white relative overflow-hidden\">\r\n          {/* Background Pattern */}\r\n          <div className=\"absolute inset-0 opacity-10\">\r\n            <div className=\"absolute top-0 left-0 w-full h-full bg-grid-white/[0.05]\"></div>\r\n          </div>\r\n          \r\n          <div className=\"relative\">\r\n            <div className=\"text-center mb-12\">\r\n              <h3 className=\"text-3xl font-bold mb-4\">Trusted by Industry Leaders</h3>\r\n              <p className=\"text-[var(--color-yellow)] text-lg max-w-2xl mx-auto\">\r\n                Our commitment to excellence is reflected in our track record and client success stories\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-8\">\r\n              {trustIndicators.map((item, index) => {\r\n                const IconComponent = item.icon;\r\n                return (\r\n                  <div key={index} className=\"text-center group\">\r\n                    <div className=\"w-16 h-16 bg-white/10 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:bg-white/20 transition-colors\">\r\n                      <IconComponent className=\"w-8 h-8 text-white\" />\r\n                    </div>\r\n                    <div className=\"text-3xl font-bold text-white mb-2\">\r\n                      {typeof item.metric === 'number' ? (\r\n                        <Counter end={item.metric} suffix={item.suffix} />\r\n                      ) : (\r\n                        <span>{item.metric}{item.suffix}</span>\r\n                      )}\r\n                    </div>\r\n                    <div className=\"text-[var(--color-yellow)] font-medium\">\r\n                      {item.label}\r\n                    </div>\r\n                  </div>\r\n                );\r\n              })}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n\r\nexport default WhyChooseUsSection;\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,yCAAyC;AACzC,MAAM,UAAU,CAAC,EAAE,GAAG,EAAE,WAAW,IAAI,EAAE,SAAS,EAAE,EAAE;IACpD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY;QAChB,MAAM,aAAa;QACnB,MAAM,WAAW;QAEjB,MAAM,UAAU,CAAC;YACf,IAAI,CAAC,WAAW,YAAY;YAC5B,MAAM,WAAW,KAAK,GAAG,CAAC,CAAC,cAAc,SAAS,IAAI,UAAU;YAEhE,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa,CAAC,WAAW,UAAU,IAAI;YACvE,SAAS;YAET,IAAI,WAAW,GAAG;gBAChB,sBAAsB;YACxB;QACF;QAEA,sBAAsB;IACxB,GAAG;QAAC;QAAK;KAAS;IAElB,qBAAO,8OAAC;;YAAM;YAAO;;;;;;;AACvB;AAEO,SAAS;IACd,MAAM,eAAe;QACnB;YACE,MAAM,sMAAA,CAAA,SAAM;YACZ,QAAQ;YACR,OAAO;YACP,UAAU;YACV,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,sMAAA,CAAA,SAAM;YACZ,QAAQ;YACR,OAAO;YACP,UAAU;YACV,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,QAAQ;YACR,OAAO;YACP,UAAU;YACV,aAAa;YACb,OAAO;QACT;KACD;IAED,MAAM,kBAAkB;QACtB;YAAE,QAAQ;YAAK,OAAO;YAA2B,MAAM,sMAAA,CAAA,SAAM;YAAE,QAAQ;QAAI;QAC3E;YAAE,QAAQ;YAAQ,OAAO;YAAuB,MAAM,oMAAA,CAAA,QAAK;YAAE,QAAQ;QAAG;QACxE;YAAE,QAAQ;YAAM,OAAO;YAAoB,MAAM,2NAAA,CAAA,cAAW;YAAE,QAAQ;QAAI;QAC1E;YAAE,QAAQ;YAAG,OAAO;YAAqB,MAAM,oMAAA,CAAA,QAAK;YAAE,QAAQ;QAAG;KAClE;IAED,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAK,WAAU;kDAA+C;;;;;;;;;;;;0CAGjE,8OAAC;gCAAG,WAAU;;oCAAkF;kDAE9F,8OAAC;wCAAK,WAAU;kDAAiC;;;;;;;;;;;;0CAKnD,8OAAC;gCAAE,WAAU;0CAA6E;;;;;;;;;;;;kCAM5F,8OAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,SAAS;4BAC1B,MAAM,gBAAgB,QAAQ,IAAI;4BAClC,MAAM,gBAAgB;gCACpB,MAAM;gCACN,QAAQ;gCACR,OAAO;4BACT;4BAEA,qBACE,8OAAC;gCAAgB,WAAU;0CAEzB,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DACb,QAAQ,MAAM;;;;;;8DAEjB,8OAAC;oDAAI,WAAW,CAAC,UAAU,EAAE,aAAa,CAAC,QAAQ,KAAK,CAAC,CAAC,iGAAiG,CAAC;8DAC1J,cAAA,8OAAC;wDAAc,WAAU;;;;;;;;;;;;;;;;;sDAK7B,8OAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK;;;;;;sDAEhB,8OAAC;4CAAE,WAAW,CAAC,2BAA2B,EACxC,QAAQ,KAAK,KAAK,SAAS,6BAC3B,QAAQ,KAAK,KAAK,WAAW,kCAAkC,8BAC/D;sDACC,QAAQ,QAAQ;;;;;;sDAEnB,8OAAC;4CAAE,WAAU;sDACV,QAAQ,WAAW;;;;;;;;;;;;+BAxBhB;;;;;wBA6Bd;;;;;;kCAIF,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;;;;;;;;;;0CAGjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA0B;;;;;;0DACxC,8OAAC;gDAAE,WAAU;0DAAuD;;;;;;;;;;;;kDAKtE,8OAAC;wCAAI,WAAU;kDACZ,gBAAgB,GAAG,CAAC,CAAC,MAAM;4CAC1B,MAAM,gBAAgB,KAAK,IAAI;4CAC/B,qBACE,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAc,WAAU;;;;;;;;;;;kEAE3B,8OAAC;wDAAI,WAAU;kEACZ,OAAO,KAAK,MAAM,KAAK,yBACtB,8OAAC;4DAAQ,KAAK,KAAK,MAAM;4DAAE,QAAQ,KAAK,MAAM;;;;;iFAE9C,8OAAC;;gEAAM,KAAK,MAAM;gEAAE,KAAK,MAAM;;;;;;;;;;;;kEAGnC,8OAAC;wDAAI,WAAU;kEACZ,KAAK,KAAK;;;;;;;+CAZL;;;;;wCAgBd;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOd;uCAEe", "debugId": null}}, {"offset": {"line": 1012, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/Home/components/LogoCarouselSection.jsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport Image from \"next/image\";\r\n\r\nconst logos = [\r\n  { src: \"/seclit-logo-1.png\", alt: \"SecurityLit\" },\r\n  { src: \"/seclit-logo-1.png\", alt: \"Enterprise Corp\" },\r\n  { src: \"/seclit-logo-1.png\", alt: \"Tech Solutions\" },\r\n  { src: \"/seclit-logo-1.png\", alt: \"Global Systems\" },\r\n  { src: \"/seclit-logo-1.png\", alt: \"Innovation Labs\" },\r\n  { src: \"/seclit-logo-1.png\", alt: \"Digital Ventures\" },\r\n  { src: \"/seclit-logo-1.png\", alt: \"Future Tech\" },\r\n  { src: \"/seclit-logo-1.png\", alt: \"Smart Solutions\" },\r\n];\r\n\r\nconst LogoCarouselSection = () => (\r\n  <section className=\"w-full bg-[var(--color-brand-navy)] py-16 px-4 overflow-hidden\">\r\n    <div className=\"max-w-7xl mx-auto\">\r\n      {/* Header */}\r\n      <div className=\"text-center mb-12\">\r\n        <h2 className=\"text-brand-light-blue text-3xl sm:text-4xl font-bold mb-4\">\r\n          Trusted by Leading Organizations\r\n        </h2>\r\n        <p className=\"text-blue-100 text-lg max-w-2xl mx-auto\">\r\n          Join hundreds of companies that rely on SecurityLit to protect their digital assets\r\n        </p>\r\n      </div>\r\n\r\n      {/* Carousel Container */}\r\n      <div className=\"relative\">\r\n        {/* Gradient Overlays */}\r\n        <div className=\"absolute left-0 top-0 w-20 h-full bg-gradient-to-r from-[var(--color-brand-navy)] to-transparent z-10\"></div>\r\n        <div className=\"absolute right-0 top-0 w-20 h-full bg-gradient-to-l from-[var(--color-brand-navy)] to-transparent z-10\"></div>\r\n        \r\n        {/* Carousel Track */}\r\n        <div className=\"overflow-hidden\">\r\n          <div className=\"carousel-track flex gap-8 sm:gap-12 md:gap-16 items-center\">\r\n            {/* First set of logos */}\r\n            {logos.map((logo, idx) => (\r\n              <div \r\n                key={`first-${idx}`} \r\n                className=\"flex-shrink-0 group hover:scale-105 transition-transform duration-300\"\r\n              >\r\n                <div className=\"w-44 h-28 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-white/20 p-6 flex items-center justify-center group-hover:border-[var(--color-brand-blue)]/50\">\r\n                  <Image\r\n                    src={logo.src}\r\n                    alt={logo.alt}\r\n                    width={140}\r\n                    height={80}\r\n                    className=\"object-contain w-full h-full filter grayscale hover:grayscale-0 transition-all duration-300 opacity-70 group-hover:opacity-100\"\r\n                    priority={idx === 0}\r\n                  />\r\n                </div>\r\n              </div>\r\n            ))}\r\n            \r\n            {/* Second set of logos for seamless loop */}\r\n            {logos.map((logo, idx) => (\r\n              <div \r\n                key={`second-${idx}`} \r\n                className=\"flex-shrink-0 group hover:scale-105 transition-transform duration-300\"\r\n              >\r\n                <div className=\"w-44 h-28 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-white/20 p-6 flex items-center justify-center group-hover:border-[var(--color-brand-blue)]/50\">\r\n                  <Image\r\n                    src={logo.src}\r\n                    alt={logo.alt}\r\n                    width={140}\r\n                    height={80}\r\n                    className=\"object-contain w-full h-full filter grayscale hover:grayscale-0 transition-all duration-300 opacity-70 group-hover:opacity-100\"\r\n                    priority={false}\r\n                  />\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <style jsx global>{`\r\n      .carousel-track {\r\n        animation: carousel-scroll 40s linear infinite;\r\n      }\r\n      \r\n      @keyframes carousel-scroll {\r\n        0% { transform: translateX(0); }\r\n        100% { transform: translateX(-50%); }\r\n      }\r\n      \r\n      .carousel-track:hover {\r\n        animation-play-state: paused;\r\n      }\r\n      \r\n      @media (max-width: 768px) {\r\n        .carousel-track {\r\n          animation: carousel-scroll 30s linear infinite;\r\n        }\r\n      }\r\n      \r\n      @media (prefers-reduced-motion: reduce) {\r\n        .carousel-track {\r\n          animation: none;\r\n        }\r\n      }\r\n    `}</style>\r\n  </section>\r\n);\r\n\r\nexport default LogoCarouselSection;\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAFA;;;;;AAIA,MAAM,QAAQ;IACZ;QAAE,KAAK;QAAsB,KAAK;IAAc;IAChD;QAAE,KAAK;QAAsB,KAAK;IAAkB;IACpD;QAAE,KAAK;QAAsB,KAAK;IAAiB;IACnD;QAAE,KAAK;QAAsB,KAAK;IAAiB;IACnD;QAAE,KAAK;QAAsB,KAAK;IAAkB;IACpD;QAAE,KAAK;QAAsB,KAAK;IAAmB;IACrD;QAAE,KAAK;QAAsB,KAAK;IAAc;IAChD;QAAE,KAAK;QAAsB,KAAK;IAAkB;CACrD;AAED,MAAM,sBAAsB,kBAC1B,8OAAC;kDAAkB;;0BACjB,8OAAC;0DAAc;;kCAEb,8OAAC;kEAAc;;0CACb,8OAAC;0EAAa;0CAA4D;;;;;;0CAG1E,8OAAC;0EAAY;0CAA0C;;;;;;;;;;;;kCAMzD,8OAAC;kEAAc;;0CAEb,8OAAC;0EAAc;;;;;;0CACf,8OAAC;0EAAc;;;;;;0CAGf,8OAAC;0EAAc;0CACb,cAAA,8OAAC;8EAAc;;wCAEZ,MAAM,GAAG,CAAC,CAAC,MAAM,oBAChB,8OAAC;0FAEW;0DAEV,cAAA,8OAAC;8FAAc;8DACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KAAK,KAAK,GAAG;wDACb,KAAK,KAAK,GAAG;wDACb,OAAO;wDACP,QAAQ;wDACR,WAAU;wDACV,UAAU,QAAQ;;;;;;;;;;;+CAVjB,CAAC,MAAM,EAAE,KAAK;;;;;wCAiBtB,MAAM,GAAG,CAAC,CAAC,MAAM,oBAChB,8OAAC;0FAEW;0DAEV,cAAA,8OAAC;8FAAc;8DACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KAAK,KAAK,GAAG;wDACb,KAAK,KAAK,GAAG;wDACb,OAAO;wDACP,QAAQ;wDACR,WAAU;wDACV,UAAU;;;;;;;;;;;+CAVT,CAAC,OAAO,EAAE,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCAiDrB", "debugId": null}}, {"offset": {"line": 1201, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/Home/components/Testimonial.jsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport Image from \"next/image\";\r\nimport { Star } from \"lucide-react\";\r\n\r\nconst testimonials = [\r\n  {\r\n    quote:\r\n      \"SecurityLit helped us identify and fix critical vulnerabilities before they became a problem. Their team is professional, responsive, and truly cares about our security.\",\r\n    name: \"<PERSON><PERSON>\",\r\n    title: \"CTO, FinTech Corp\",\r\n    avatar: \"/seclit-logo-1.png\",\r\n    initials: \"PS\",\r\n  },\r\n  {\r\n    quote:\r\n      \"The vCISO service from SecurityLit gave us the strategic guidance we needed to scale securely. Highly recommended for any growing business!\",\r\n    name: \"<PERSON>\",\r\n    title: \"Founder, HealthSync\",\r\n    avatar: \"/seclit-logo-1.png\",\r\n    initials: \"<PERSON><PERSON>\",\r\n  },\r\n  {\r\n    quote:\r\n      \"Their red teaming and compliance assessment were top-notch. We now feel confident about our security posture and compliance readiness.\",\r\n    name: \"<PERSON><PERSON><PERSON>\",\r\n    title: \"COO, EduTech Solutions\",\r\n    avatar: \"/seclit-logo-1.png\",\r\n    initials: \"AP\",\r\n  },\r\n  {\r\n    quote:\r\n      \"SecurityLit transformed our entire security posture. Their 24/7 monitoring and incident response capabilities give us complete peace of mind.\",\r\n    name: \"<PERSON>\",\r\n    title: \"<PERSON><PERSON><PERSON>, CloudFirst\",\r\n    avatar: \"/seclit-logo-1.png\",\r\n    initials: \"SR\",\r\n  },\r\n  {\r\n    quote:\r\n      \"The compliance pre-assessment saved us months of preparation time for our audit. Their expertise in regulatory frameworks is unmatched.\",\r\n    name: \"David Chen\",\r\n    title: \"VP Security, DataSecure\",\r\n    avatar: \"/seclit-logo-1.png\",\r\n    initials: \"DC\",\r\n  },\r\n  {\r\n    quote:\r\n      \"Working with SecurityLit has been transformational. Their proactive approach and advanced technology stack prevented multiple security incidents.\",\r\n    name: \"Emily Parker\",\r\n    title: \"IT Director, RetailMax\",\r\n    avatar: \"/seclit-logo-1.png\",\r\n    initials: \"EP\",\r\n  },\r\n];\r\n\r\nconst Testimonial = () => (\r\n  <section className=\"py-24 bg-gradient-to-br from-slate-50 via-white to-blue-50/30 relative overflow-hidden\">\r\n    {/* Background Elements */}\r\n    <div className=\"absolute top-0 left-0 w-full h-full bg-grid-slate-100/30 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))]\"></div>\r\n    <div className=\"absolute top-0 right-0 w-72 h-72 bg-gradient-to-bl from-[var(--color-brand-blue)]/20 to-transparent rounded-full blur-3xl\"></div>\r\n    <div className=\"absolute bottom-0 left-0 w-72 h-72 bg-gradient-to-tr from-[var(--color-brand-light)]/20 to-transparent rounded-full blur-3xl\"></div>\r\n\r\n    <div className=\"w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\">\r\n      <div className=\"text-center space-y-6 mb-20 fade-in-up\">\r\n        \r\n        <h2 className=\"text-4xl font-bold text-[var(--color-brand-navy)] sm:text-5xl lg:text-6xl\">\r\n          What Our Clients\r\n          <span className=\"block bg-gradient-to-r from-[var(--color--blue)] via-[var(--color-blue)] to-[var(--color-brand-blue-alt)] bg-clip-text text-transparent\">\r\n            Say About Us\r\n          </span>\r\n        </h2>\r\n        <p className=\"text-xl text-[var(--foreground-secondary)] max-w-4xl mx-auto leading-relaxed\">\r\n          Don't just take our word for it. Here's what industry leaders and security professionals say about working\r\n          with SecurityLit\r\n        </p>\r\n      </div>\r\n\r\n      {/* Moving Testimonials Container */}\r\n      <div className=\"relative\">\r\n        {/* First Row - Moving Right */}\r\n        <div className=\"flex space-x-6 animate-scroll-right mb-8\">\r\n          {/* Map through testimonials */}\r\n          {testimonials.map((testimonial, idx) => (\r\n            <div key={`first-${idx}`} className=\"flex-shrink-0 w-96 bg-white/80 backdrop-blur-sm border border-[var(--color-brand-blue)]/20 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300\">\r\n              <div className=\"flex items-center space-x-1 mb-6\">\r\n                {[...Array(5)].map((_, i) => (\r\n                  <Star key={i} className=\"h-4 w-4 text-yellow-400 fill-current\" />\r\n                ))}\r\n              </div>\r\n              <blockquote className=\"text-[var(--color-brand-navy)] leading-relaxed mb-6\">\r\n                \"{testimonial.quote}\"\r\n              </blockquote>\r\n              <div className=\"flex items-center space-x-4\">\r\n                <div className=\"w-12 h-12 bg-gradient-to-br from-[var(--color-brand-blue)] to-[var(--color-brand-navy)] rounded-full flex items-center justify-center\">\r\n                  <span className=\"text-white font-bold text-sm\">{testimonial.initials}</span>\r\n                </div>\r\n                <div>\r\n                  <div className=\"font-bold text-[var(--color-brand-navy)]\">{testimonial.name}</div>\r\n                  <div className=\"text-[var(--foreground-secondary)] text-sm\">{testimonial.title}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n\r\n          {/* Duplicate testimonials for seamless loop */}\r\n          {testimonials.map((testimonial, idx) => (\r\n            <div key={`duplicate-${idx}`} className=\"flex-shrink-0 w-96 bg-white/80 backdrop-blur-sm border border-[var(--color-brand-blue)]/20 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300\">\r\n              <div className=\"flex items-center space-x-1 mb-6\">\r\n                {[...Array(5)].map((_, i) => (\r\n                  <Star key={i} className=\"h-4 w-4 text-yellow-400 fill-current\" />\r\n                ))}\r\n              </div>\r\n              <blockquote className=\"text-[var(--color-brand-navy)] leading-relaxed mb-6\">\r\n                \"{testimonial.quote}\"\r\n              </blockquote>\r\n              <div className=\"flex items-center space-x-4\">\r\n                <div className=\"w-12 h-12 bg-gradient-to-br from-[var(--color-brand-blue)] to-[var(--color-brand-navy)] rounded-full flex items-center justify-center\">\r\n                  <span className=\"text-white font-bold text-sm\">{testimonial.initials}</span>\r\n                </div>\r\n                <div>\r\n                  <div className=\"font-bold text-[var(--color-brand-navy)]\">{testimonial.name}</div>\r\n                  <div className=\"text-[var(--foreground-secondary)] text-sm\">{testimonial.title}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Trust Indicators */}\r\n     \r\n    </div>\r\n\r\n    {/* Custom CSS for animations */}\r\n    <style jsx>{`\r\n      @keyframes scroll-right {\r\n        0% {\r\n          transform: translateX(-100%);\r\n        }\r\n        100% {\r\n          transform: translateX(0%);\r\n        }\r\n      }\r\n\r\n      .animate-scroll-right {\r\n        animation: scroll-right 40s linear infinite;\r\n      }\r\n\r\n      .animate-scroll-right:hover {\r\n        animation-play-state: paused;\r\n      }\r\n\r\n      @media (max-width: 768px) {\r\n        .animate-scroll-right {\r\n          animation: scroll-right 30s linear infinite;\r\n        }\r\n      }\r\n\r\n      @media (prefers-reduced-motion: reduce) {\r\n        .animate-scroll-right {\r\n          animation: none;\r\n        }\r\n      }\r\n    `}</style>\r\n  </section>\r\n);\r\n\r\nexport default Testimonial;\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AAHA;;;;;;AAKA,MAAM,eAAe;IACnB;QACE,OACE;QACF,MAAM;QACN,OAAO;QACP,QAAQ;QACR,UAAU;IACZ;IACA;QACE,OACE;QACF,MAAM;QACN,OAAO;QACP,QAAQ;QACR,UAAU;IACZ;IACA;QACE,OACE;QACF,MAAM;QACN,OAAO;QACP,QAAQ;QACR,UAAU;IACZ;IACA;QACE,OACE;QACF,MAAM;QACN,OAAO;QACP,QAAQ;QACR,UAAU;IACZ;IACA;QACE,OACE;QACF,MAAM;QACN,OAAO;QACP,QAAQ;QACR,UAAU;IACZ;IACA;QACE,OACE;QACF,MAAM;QACN,OAAO;QACP,QAAQ;QACR,UAAU;IACZ;CACD;AAED,MAAM,cAAc,kBAClB,8OAAC;kDAAkB;;0BAEjB,8OAAC;0DAAc;;;;;;0BACf,8OAAC;0DAAc;;;;;;0BACf,8OAAC;0DAAc;;;;;;0BAEf,8OAAC;0DAAc;;kCACb,8OAAC;kEAAc;;0CAEb,8OAAC;0EAAa;;oCAA4E;kDAExF,8OAAC;kFAAe;kDAA0I;;;;;;;;;;;;0CAI5J,8OAAC;0EAAY;0CAA+E;;;;;;;;;;;;kCAO9F,8OAAC;kEAAc;kCAEb,cAAA,8OAAC;sEAAc;;gCAEZ,aAAa,GAAG,CAAC,CAAC,aAAa,oBAC9B,8OAAC;kFAAmC;;0DAClC,8OAAC;0FAAc;0DACZ;uDAAI,MAAM;iDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;wDAAS,WAAU;uDAAb;;;;;;;;;;0DAGf,8OAAC;0FAAqB;;oDAAsD;oDACxE,YAAY,KAAK;oDAAC;;;;;;;0DAEtB,8OAAC;0FAAc;;kEACb,8OAAC;kGAAc;kEACb,cAAA,8OAAC;sGAAe;sEAAgC,YAAY,QAAQ;;;;;;;;;;;kEAEtE,8OAAC;;;0EACC,8OAAC;0GAAc;0EAA4C,YAAY,IAAI;;;;;;0EAC3E,8OAAC;0GAAc;0EAA8C,YAAY,KAAK;;;;;;;;;;;;;;;;;;;uCAf1E,CAAC,MAAM,EAAE,KAAK;;;;;gCAsBzB,aAAa,GAAG,CAAC,CAAC,aAAa,oBAC9B,8OAAC;kFAAuC;;0DACtC,8OAAC;0FAAc;0DACZ;uDAAI,MAAM;iDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;wDAAS,WAAU;uDAAb;;;;;;;;;;0DAGf,8OAAC;0FAAqB;;oDAAsD;oDACxE,YAAY,KAAK;oDAAC;;;;;;;0DAEtB,8OAAC;0FAAc;;kEACb,8OAAC;kGAAc;kEACb,cAAA,8OAAC;sGAAe;sEAAgC,YAAY,QAAQ;;;;;;;;;;;kEAEtE,8OAAC;;;0EACC,8OAAC;0GAAc;0EAA4C,YAAY,IAAI;;;;;;0EAC3E,8OAAC;0GAAc;0EAA8C,YAAY,KAAK;;;;;;;;;;;;;;;;;;;uCAf1E,CAAC,UAAU,EAAE,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCA6DzB", "debugId": null}}, {"offset": {"line": 1530, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/Home/components/ContactUsSection.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from \"react\";\r\nimport { ArrowRight, Mail, Phone, MapPin } from \"lucide-react\";\r\n\r\nexport function ContactUsSection() {\r\n  const [formData, setFormData] = useState({\r\n    name: '',\r\n    email: '',\r\n    company: '',\r\n    message: ''\r\n  });\r\n\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n\r\n  const handleInputChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: value\r\n    }));\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setIsSubmitting(true);\r\n    setTimeout(() => {\r\n      setIsSubmitting(false);\r\n      alert('Thank you! We\\'ll get back to you within 24 hours.');\r\n      setFormData({ name: '', email: '', company: '', message: '' });\r\n    }, 2000);\r\n  };\r\n\r\n  return (\r\n    <section className=\"py-24 bg-gray-50 relative overflow-hidden\">\r\n      {/* Background Pattern */}\r\n      <div className=\"absolute inset-0\">\r\n        <div className=\"absolute top-0 right-1/4 w-72 h-72 bg-[var(--color-blue)]/5 rounded-full blur-3xl\"></div>\r\n        <div className=\"absolute bottom-0 left-1/4 w-96 h-96 bg-[var(--color-yellow)]/5 rounded-full blur-3xl\"></div>\r\n      </div>\r\n\r\n      <div className=\"w-full max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative\">\r\n        {/* Header Section */}\r\n        <div className=\"text-center mb-16 fade-in-up\">\r\n          <div className=\"inline-flex items-center bg-[var(--color-blue)]/10 px-4 py-2 rounded-full mb-6\">\r\n            <div className=\"w-2 h-2 bg-[var(--color-blue)] rounded-full mr-2\"></div>\r\n            <span className=\"text-sm font-medium text-[var(--color-blue)]\">Get In Touch</span>\r\n          </div>\r\n          \r\n          <h2 className=\"text-4xl md:text-5xl lg:text-6xl font-bold text-[var(--color-dark-blue)] mb-6 leading-tight\">\r\n            Ready to\r\n            <span className=\"block text-[var(--color-blue)]\">\r\n              Secure Your Business?\r\n            </span>\r\n          </h2>\r\n          \r\n          <p className=\"text-xl text-[var(--color-dark-blue)]/70 max-w-3xl mx-auto leading-relaxed\">\r\n            Connect with our cybersecurity experts for a free consultation and discover how we can protect your organization.\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"grid lg:grid-cols-2 gap-12 items-start\">\r\n          {/* Left: Content */}\r\n          <div className=\"space-y-8\">\r\n            <div className=\"bg-white rounded-2xl p-8 shadow-md border border-gray-100 fade-in-up\">\r\n              <div className=\"flex items-center gap-4 mb-6\">\r\n                <div className=\"w-14 h-14 bg-[var(--color-blue)] rounded-xl flex items-center justify-center shadow-lg\">\r\n                  <Mail className=\"w-7 h-7 text-white\" />\r\n                </div>\r\n                <div>\r\n                  <h3 className=\"text-2xl font-bold text-[var(--color-dark-blue)]\">Contact Information</h3>\r\n                  <p className=\"text-[var(--color-dark-blue)]/70\">Get in touch with our team</p>\r\n                </div>\r\n              </div>\r\n              \r\n              <div className=\"space-y-6\">\r\n                <div className=\"flex items-center gap-4\">\r\n                  <div className=\"w-10 h-10 bg-[var(--color-blue)]/10 rounded-lg flex items-center justify-center\">\r\n                    <Mail className=\"w-5 h-5 text-[var(--color-blue)]\" />\r\n                  </div>\r\n                  <div>\r\n                    <p className=\"font-semibold text-[var(--color-dark-blue)]\">Email</p>\r\n                    <p className=\"text-[var(--color-blue)]\"><EMAIL></p>\r\n                  </div>\r\n                </div>\r\n                \r\n                <div className=\"flex items-center gap-4\">\r\n                  <div className=\"w-10 h-10 bg-[var(--color-blue)]/10 rounded-lg flex items-center justify-center\">\r\n                    <Phone className=\"w-5 h-5 text-[var(--color-blue)]\" />\r\n                  </div>\r\n                  <div>\r\n                    <p className=\"font-semibold text-[var(--color-dark-blue)]\">Phone</p>\r\n                    <p className=\"text-[var(--color-blue)]\">+91 8527 800769</p>\r\n                  </div>\r\n                </div>\r\n                \r\n                <div className=\"flex items-center gap-4\">\r\n                  <div className=\"w-10 h-10 bg-[var(--color-blue)]/10 rounded-lg flex items-center justify-center\">\r\n                    <MapPin className=\"w-5 h-5 text-[var(--color-blue)]\" />\r\n                  </div>\r\n                  <div>\r\n                    <p className=\"font-semibold text-[var(--color-dark-blue)]\">Office</p>\r\n                    <p className=\"text-[var(--color-dark-blue)]/70\">Gurugram, Haryana, India</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"bg-[var(--color-dark-blue)] rounded-2xl p-8 text-white fade-in-up\">\r\n              <h4 className=\"text-xl font-bold mb-4\">Why Choose SecurityLit?</h4>\r\n              <ul className=\"space-y-3 text-[var(--color-yellow)]/90\">\r\n                <li className=\"flex items-center gap-3\">\r\n                  <div className=\"w-2 h-2 bg-[var(--color-yellow)] rounded-full\"></div>\r\n                  <span>Free initial consultation</span>\r\n                </li>\r\n                <li className=\"flex items-center gap-3\">\r\n                  <div className=\"w-2 h-2 bg-[var(--color-yellow)] rounded-full\"></div>\r\n                  <span>24/7 expert support</span>\r\n                </li>\r\n                <li className=\"flex items-center gap-3\">\r\n                  <div className=\"w-2 h-2 bg-[var(--color-yellow)] rounded-full\"></div>\r\n                  <span>Customized security solutions</span>\r\n                </li>\r\n                <li className=\"flex items-center gap-3\">\r\n                  <div className=\"w-2 h-2 bg-[var(--color-yellow)] rounded-full\"></div>\r\n                  <span>Proven track record</span>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Right: Form */}\r\n          <div className=\"bg-white rounded-2xl shadow-xl p-8 border border-gray-100 fade-in-up\">\r\n            <div className=\"mb-8\">\r\n              <h3 className=\"text-2xl font-bold text-[var(--color-dark-blue)] mb-2\">Send us a message</h3>\r\n              <p className=\"text-[var(--color-dark-blue)]/70\">Fill out the form below and we'll get back to you within 24 hours.</p>\r\n            </div>\r\n\r\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n              <div className=\"grid md:grid-cols-2 gap-4\">\r\n                <div>\r\n                  <label className=\"block text-sm font-semibold text-[var(--color-dark-blue)] mb-2\">Name *</label>\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"name\"\r\n                    value={formData.name}\r\n                    onChange={handleInputChange}\r\n                    required\r\n                    className=\"w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:bg-white transition-all\"\r\n                    placeholder=\"Your name\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-semibold text-[var(--color-dark-blue)] mb-2\">Email *</label>\r\n                  <input\r\n                    type=\"email\"\r\n                    name=\"email\"\r\n                    value={formData.email}\r\n                    onChange={handleInputChange}\r\n                    required\r\n                    className=\"w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:bg-white transition-all\"\r\n                    placeholder=\"<EMAIL>\"\r\n                  />\r\n                </div>\r\n              </div>\r\n              \r\n              <div>\r\n                <label className=\"block text-sm font-semibold text-[var(--color-dark-blue)] mb-2\">Company</label>\r\n                <input\r\n                  type=\"text\"\r\n                  name=\"company\"\r\n                  value={formData.company}\r\n                  onChange={handleInputChange}\r\n                  className=\"w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:bg-white transition-all\"\r\n                  placeholder=\"Your company\"\r\n                />\r\n              </div>\r\n              \r\n              <div>\r\n                <label className=\"block text-sm font-semibold text-[var(--color-dark-blue)] mb-2\">Message</label>\r\n                <textarea\r\n                  name=\"message\"\r\n                  value={formData.message}\r\n                  onChange={handleInputChange}\r\n                  rows=\"4\"\r\n                  className=\"w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:bg-white transition-all resize-none\"\r\n                  placeholder=\"Tell us about your security needs...\"\r\n                ></textarea>\r\n              </div>\r\n              \r\n              <button\r\n                type=\"submit\"\r\n                disabled={isSubmitting}\r\n                className=\"w-full bg-[var(--color-blue)] hover:bg-[var(--color-blue-secondary)] disabled:bg-gray-400 text-white px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center gap-2\"\r\n              >\r\n                {isSubmitting ? (\r\n                  <>\r\n                    <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"></div>\r\n                    Sending Message...\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    Send Message\r\n                    <ArrowRight className=\"w-5 h-5\" />\r\n                  </>\r\n                )}\r\n              </button>\r\n            </form>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n\r\nexport default ContactUsSection;\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAKO,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;IACX;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAChB,WAAW;YACT,gBAAgB;YAChB,MAAM;YACN,YAAY;gBAAE,MAAM;gBAAI,OAAO;gBAAI,SAAS;gBAAI,SAAS;YAAG;QAC9D,GAAG;IACL;IAEA,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAK,WAAU;kDAA+C;;;;;;;;;;;;0CAGjE,8OAAC;gCAAG,WAAU;;oCAA8F;kDAE1G,8OAAC;wCAAK,WAAU;kDAAiC;;;;;;;;;;;;0CAKnD,8OAAC;gCAAE,WAAU;0CAA6E;;;;;;;;;;;;kCAK5F,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAmD;;;;;;0EACjE,8OAAC;gEAAE,WAAU;0EAAmC;;;;;;;;;;;;;;;;;;0DAIpD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;0EAElB,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFAA8C;;;;;;kFAC3D,8OAAC;wEAAE,WAAU;kFAA2B;;;;;;;;;;;;;;;;;;kEAI5C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;;;;;;0EAEnB,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFAA8C;;;;;;kFAC3D,8OAAC;wEAAE,WAAU;kFAA2B;;;;;;;;;;;;;;;;;;kEAI5C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;0EAEpB,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFAA8C;;;;;;kFAC3D,8OAAC;wEAAE,WAAU;kFAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAMxD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAyB;;;;;;0DACvC,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAwD;;;;;;0DACtE,8OAAC;gDAAE,WAAU;0DAAmC;;;;;;;;;;;;kDAGlD,8OAAC;wCAAK,UAAU;wCAAc,WAAU;;0DACtC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAAiE;;;;;;0EAClF,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,IAAI;gEACpB,UAAU;gEACV,QAAQ;gEACR,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAGhB,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAAiE;;;;;;0EAClF,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,KAAK;gEACrB,UAAU;gEACV,QAAQ;gEACR,WAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAiE;;;;;;kEAClF,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAIhB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAiE;;;;;;kEAClF,8OAAC;wDACC,MAAK;wDACL,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,MAAK;wDACL,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAIhB,8OAAC;gDACC,MAAK;gDACL,UAAU;gDACV,WAAU;0DAET,6BACC;;sEACE,8OAAC;4DAAI,WAAU;;;;;;wDAAkE;;iFAInF;;wDAAE;sEAEA,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1C;uCAEe", "debugId": null}}]}