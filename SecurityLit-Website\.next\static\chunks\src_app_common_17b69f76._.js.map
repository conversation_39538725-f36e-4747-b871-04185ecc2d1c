{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/common/buttons/BrandButtons.jsx"], "sourcesContent": ["// src/app/common/BrandButtons.jsx\r\n\r\nimport React from \"react\";\r\n\r\n// Primary blue button\r\nexport function PrimaryButton({ children, className = \"\", ...props }) {\r\n  return (\r\n    <button\r\n      className={`btn-primary text-white px-8 py-3 rounded-lg font-bold text-lg shadow-lg flex items-center justify-center gap-2 cursor-pointer ${className}`}\r\n      {...props}\r\n    >\r\n      {children}\r\n    </button>\r\n  );\r\n}\r\n\r\n// Secondary navy blue button\r\nexport function SecondaryButton({ children, className = \"\", ...props }) {\r\n  return (\r\n    <button\r\n      className={`btn-secondary text-white px-8 py-3 rounded-lg font-bold text-lg shadow-md flex items-center justify-center gap-2 cursor-pointer ${className}`}\r\n      {...props}\r\n    >\r\n      {children}\r\n    </button>\r\n  );\r\n}\r\n\r\n// Outlined button for secondary actions\r\nexport function OutlinedButton({ children, className = \"\", ...props }) {\r\n  return (\r\n    <button\r\n      className={`btn-outlined text-[var(--color-dark-blue)] px-8 py-3 rounded-lg font-bold text-lg shadow-md flex items-center justify-center gap-2 cursor-pointer ${className}`}\r\n      {...props}\r\n    >\r\n      {children}\r\n    </button>\r\n  );\r\n}\r\n\r\n// Accent yellow button\r\nexport function AccentButton({ children, className = \"\", ...props }) {\r\n  return (\r\n    <button\r\n      className={`btn-accent text-[var(--color-gray)] px-8 py-3 rounded-lg font-bold text-lg shadow-md flex items-center justify-center gap-2 cursor-pointer ${className}`}\r\n      {...props}\r\n    >\r\n      {children}\r\n    </button>\r\n  );\r\n}\r\n\r\n// Form submit button (full width)\r\nexport function SubmitButton({ children, className = \"\", ...props }) {\r\n  return (\r\n    <button\r\n      type=\"submit\"\r\n      className={`w-full bg-[var(--color-blue)] hover:bg-[var(--color-blue-secondary)] text-white py-4 px-6 rounded-lg font-semibold text-lg transition-all shadow-lg hover:shadow-xl ${className}`}\r\n      {...props}\r\n    >\r\n      {children}\r\n    </button>\r\n  );\r\n}\r\n\r\n// Call to action button (white background, blue text)\r\nexport function CallToActionButton({ children, className = \"\", ...props }) {\r\n  return (\r\n    <button\r\n      className={`bg-white text-[var(--color-blue)] px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors ${className}`}\r\n      {...props}\r\n    >\r\n      {children}\r\n    </button>\r\n  );\r\n}\r\n\r\n// Outlined call to action button (white border, white text)\r\nexport function OutlinedCallToActionButton({ children, className = \"\", ...props }) {\r\n  return (\r\n    <button\r\n      className={`border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-[var(--color-blue)] transition-colors ${className}`}\r\n      {...props}\r\n    >\r\n      {children}\r\n    </button>\r\n  );\r\n}\r\n\r\n// Link button (for phone/email links)\r\nexport function LinkButton({ href, children, className = \"\", ...props }) {\r\n  return (\r\n    <a\r\n      href={href}\r\n      className={`bg-white text-[var(--color-blue)] px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors inline-block text-center ${className}`}\r\n      {...props}\r\n    >\r\n      {children}\r\n    </a>\r\n  );\r\n}\r\n\r\n// Outlined link button (for phone/email links)\r\nexport function OutlinedLinkButton({ href, children, className = \"\", ...props }) {\r\n  return (\r\n    <a\r\n      href={href}\r\n      className={`border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-[var(--color-blue)] transition-colors inline-block text-center ${className}`}\r\n      {...props}\r\n    >\r\n      {children}\r\n    </a>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,kCAAkC;;;;;;;;;;;;;AAElC;;;AAGO,SAAS,cAAc,KAAsC;QAAtC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE,GAAG,OAAO,GAAtC;IAC5B,qBACE,6LAAC;QACC,WAAW,AAAC,iIAA0I,OAAV;QAC3I,GAAG,KAAK;kBAER;;;;;;AAGP;KATgB;AAYT,SAAS,gBAAgB,KAAsC;QAAtC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE,GAAG,OAAO,GAAtC;IAC9B,qBACE,6LAAC;QACC,WAAW,AAAC,mIAA4I,OAAV;QAC7I,GAAG,KAAK;kBAER;;;;;;AAGP;MATgB;AAYT,SAAS,eAAe,KAAsC;QAAtC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE,GAAG,OAAO,GAAtC;IAC7B,qBACE,6LAAC;QACC,WAAW,AAAC,qJAA8J,OAAV;QAC/J,GAAG,KAAK;kBAER;;;;;;AAGP;MATgB;AAYT,SAAS,aAAa,KAAsC;QAAtC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE,GAAG,OAAO,GAAtC;IAC3B,qBACE,6LAAC;QACC,WAAW,AAAC,8IAAuJ,OAAV;QACxJ,GAAG,KAAK;kBAER;;;;;;AAGP;MATgB;AAYT,SAAS,aAAa,KAAsC;QAAtC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE,GAAG,OAAO,GAAtC;IAC3B,qBACE,6LAAC;QACC,MAAK;QACL,WAAW,AAAC,uKAAgL,OAAV;QACjL,GAAG,KAAK;kBAER;;;;;;AAGP;MAVgB;AAaT,SAAS,mBAAmB,KAAsC;QAAtC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE,GAAG,OAAO,GAAtC;IACjC,qBACE,6LAAC;QACC,WAAW,AAAC,2GAAoH,OAAV;QACrH,GAAG,KAAK;kBAER;;;;;;AAGP;MATgB;AAYT,SAAS,2BAA2B,KAAsC;QAAtC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE,GAAG,OAAO,GAAtC;IACzC,qBACE,6LAAC;QACC,WAAW,AAAC,uIAAgJ,OAAV;QACjJ,GAAG,KAAK;kBAER;;;;;;AAGP;MATgB;AAYT,SAAS,WAAW,KAA4C;QAA5C,EAAE,IAAI,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE,GAAG,OAAO,GAA5C;IACzB,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,AAAC,oIAA6I,OAAV;QAC9I,GAAG,KAAK;kBAER;;;;;;AAGP;MAVgB;AAaT,SAAS,mBAAmB,KAA4C;QAA5C,EAAE,IAAI,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE,GAAG,OAAO,GAA5C;IACjC,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,AAAC,gKAAyK,OAAV;QAC1K,GAAG,KAAK;kBAER;;;;;;AAGP;MAVgB", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/common/Header.jsx"], "sourcesContent": ["// src/app/common/Header.jsx\r\n\"use client\";\r\n\r\nimport React, { useState, useEffect, useRef } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { PrimaryButton } from \"@/app/common/buttons/BrandButtons\";\r\nimport { FaChevronDown, FaBars, FaTimes } from \"react-icons/fa\";\r\n\r\nexport default function Header() {\r\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\r\n  const [servicesDropdownOpen, setServicesDropdownOpen] = useState(false);\r\n  const [resourcesDropdownOpen, setResourcesDropdownOpen] = useState(false);\r\n  const [ openMobileDropdown, setOpenMobileDropdown] = useState(null);\r\n  const [isScrolled, setIsScrolled] = useState(false);\r\n  const [isVisible, setIsVisible] = useState(true);\r\n  const [lastScrollY, setLastScrollY] = useState(0);\r\n\r\n  // CHANGED: Added a separate ref for the dropdown panel\r\n  const dropdownTriggerRef = useRef(null);\r\n  const dropdownPanelRef = useRef(null);\r\n\r\n  // Professional scroll detection for navbar hide/show and glass effect\r\n  useEffect(() => {\r\n    let ticking = false;\r\n    let scrollThreshold = 5; // Minimum scroll distance to trigger hide/show\r\n    \r\n    const handleScroll = () => {\r\n      if (!ticking) {\r\n        requestAnimationFrame(() => {\r\n          const currentScrollY = window.scrollY;\r\n          const scrollDifference = Math.abs(currentScrollY - lastScrollY);\r\n          \r\n          // Glass effect\r\n          setIsScrolled(currentScrollY > 50);\r\n          \r\n          // Only trigger hide/show if scroll difference is significant\r\n          if (scrollDifference > scrollThreshold) {\r\n            // Navbar hide/show logic with improved threshold\r\n            if (currentScrollY > lastScrollY && currentScrollY > 80) {\r\n              // Scrolling down - hide navbar\r\n              setIsVisible(false);\r\n              console.log('Hiding navbar - scrolling down', currentScrollY);\r\n            } else if (currentScrollY < lastScrollY || currentScrollY <= 80) {\r\n              // Scrolling up or near top - show navbar\r\n              setIsVisible(true);\r\n              console.log('Showing navbar - scrolling up or at top', currentScrollY);\r\n            }\r\n            \r\n            setLastScrollY(currentScrollY);\r\n          }\r\n          \r\n          ticking = false;\r\n        });\r\n        ticking = true;\r\n      }\r\n    };\r\n\r\n    window.addEventListener('scroll', handleScroll, { passive: true });\r\n    return () => window.removeEventListener('scroll', handleScroll);\r\n  }, [lastScrollY]);\r\n\r\n    // Navigation links structure (no changes)\r\n  const navLinks = [\r\n    {\r\n      name: \"Services\",\r\n      dropdown: [\r\n        { name: \"Virtual CISO (vCISO)\", description: \"Expert cybersecurity leadership and strategic guidance\", href: \"/services/vciso\" },\r\n        { name: \"Red Teaming\", description: \"Simulated cyberattacks to test and strengthen defenses\", href: \"/services/red-teaming\" },\r\n        { name: \"AWS and Azure Configuration\", description: \"Secure cloud infrastructure setup and management\", href: \"/services/aws-azure\" },\r\n        { name: \"Web3 Audits (Pentest)\", description: \"Comprehensive security audits for blockchain projects\", href: \"/services/web3-audits\" },\r\n        { name: \"VDP\", description: \"Vulnerability Disclosure Program management\", href: \"/services/vdp\" },\r\n        { name: \"VAPT\", description: \"Vulnerability Assessment and Penetration Testing\", href: \"/services/vapt\" },\r\n        { name: \"Bug Bounty (Through Capture The Bug)\", description: \"Crowdsourced security testing platform\", href: \"/services/bug-bounty\" },\r\n        { name: \"Compliance Pre Assessment\", description: \"Get audit-ready with compliance health checks\", href: \"/services/compliance\" },\r\n        { name: \"Office365 Assessment\", description: \"Security evaluation of Microsoft Office 365 environment\", href: \"/services/office365\" },\r\n        { name: \"Cloud Assessment\", description: \"Comprehensive cloud security evaluation\", href: \"/services/cloud-assessment\" },\r\n        { name: \"Google WorkSpace Assessment\", description: \"Security assessment of Google Workspace setup\", href: \"/services/google-workspace\" },\r\n        { name: \"Incident response\", description: \"Rapid response to security incidents and crisis management\", href: \"/services/incident-response\" },\r\n        { name: \"Source code review\", description: \"In-depth analysis of application source code security\", href: \"/services/source-code-review\" },\r\n      ],\r\n    },\r\n    { name: \"Security Training\", href: \"/training\" },\r\n    { name: \"Product\", href: \"/product\" },\r\n    {\r\n      name: \"Resources\",\r\n      dropdown: [\r\n        { name: \"Blogs\", description: \"Latest cybersecurity insights and industry updates\", href: \"/blogs\" },\r\n        { name: \"News\", description: \"Latest security news and updates\", href: \"/news\" },\r\n      ],\r\n    },\r\n    { name: \"About Us\", href: \"/about\" },\r\n  ];\r\n\r\n  // CHANGED: Updated the effect to check both refs\r\n  useEffect(() => {\r\n    function handleClickOutside(event) {\r\n      if (\r\n        dropdownTriggerRef.current &&\r\n        !dropdownTriggerRef.current.contains(event.target) &&\r\n        dropdownPanelRef.current &&\r\n        !dropdownPanelRef.current.contains(event.target)\r\n      ) {\r\n        setServicesDropdownOpen(false);\r\n        setResourcesDropdownOpen(false);\r\n      }\r\n    }\r\n    document.addEventListener(\"mousedown\", handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, []); // Dependencies are not needed as refs don't trigger re-renders\r\n\r\n  // REMOVED: The complex handleDownload function is no longer needed.\r\n\r\n  const NavLink = ({ item }) => (\r\n    <Link href={item.href} className=\"px-4 py-2 hover:text-[var(--color-yellow)] transition-colors font-medium text-white\">\r\n      {item.name}\r\n    </Link>\r\n  );\r\n\r\n  return (\r\n    <header className={`fixed top-0 left-0 w-full z-[9999] py-2 mb-16 md:mb-0 transition-all duration-300 ease-in-out ${isVisible ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'}`}>\r\n      <div className=\"w-full max-w-7xl mx-auto px-2 sm:px-4 lg:px-0 relative\">\r\n        <div className={`navbar-glass ${isScrolled ? 'scrolled' : ''} rounded-4xl shadow-lg border border-gray-700`}>\r\n          <div className=\"flex items-center justify-between h-16 px-6\">\r\n            {/* Logo */}\r\n            <div className=\"flex-shrink-0\">\r\n              <Link href=\"/\" className=\"flex items-center gap-2\">\r\n                <img src=\"/seclit-logo-white.png\" alt=\"SecurityLit Logo\" className=\"h-12 w-auto\" />\r\n              </Link>\r\n            </div>\r\n\r\n            {/* Desktop Navigation */}\r\n            <nav className=\"hidden lg:flex items-center space-x-8\">\r\n              {navLinks.map((link) =>\r\n                link.dropdown ? (\r\n                  // CHANGED: Attached the trigger ref here\r\n                                     <div \r\n                     key={link.name} \r\n                     className=\"relative\" \r\n                     ref={link.name === \"Services\" ? dropdownTriggerRef : null}\r\n                   >\r\n                     <button\r\n                       onClick={() => {\r\n                         if (link.name === \"Services\") {\r\n                           setServicesDropdownOpen(!servicesDropdownOpen);\r\n                           setResourcesDropdownOpen(false);\r\n                         } else if (link.name === \"Resources\") {\r\n                           setResourcesDropdownOpen(!resourcesDropdownOpen);\r\n                           setServicesDropdownOpen(false);\r\n                         }\r\n                       }}\r\n                       onMouseEnter={() => {\r\n                         if (link.name === \"Services\") {\r\n                           setServicesDropdownOpen(true);\r\n                           setResourcesDropdownOpen(false);\r\n                         } else if (link.name === \"Resources\") {\r\n                           setResourcesDropdownOpen(true);\r\n                           setServicesDropdownOpen(false);\r\n                         }\r\n                       }}\r\n                       className=\"flex items-center gap-2 px-4 py-2 hover:text-[var(--color-yellow)] transition-colors font-medium text-white\"\r\n                     >\r\n                      {link.name}\r\n                      <FaChevronDown className={`h-3 w-3 transition-transform duration-300 ${(link.name === \"Services\" && servicesDropdownOpen) || (link.name === \"Resources\" && resourcesDropdownOpen) ? 'transform rotate-180' : ''}`} />\r\n                    </button>\r\n                  </div>\r\n                ) : (\r\n                  <NavLink key={link.name} item={link} />\r\n                )\r\n              )}\r\n            </nav>\r\n\r\n            {/* Contact Button */}\r\n            <div className=\"hidden lg:block\">\r\n              <PrimaryButton onClick={() => window.location.href='/contact'} className=\"px-3 py-1.5 lg:px-4 lg:py-2 text-sm\">\r\n                Contact Us\r\n              </PrimaryButton>\r\n            </div>\r\n\r\n            {/* Mobile Menu Button */}\r\n            <div className=\"lg:hidden\">\r\n              <button onClick={() => setMobileMenuOpen(!mobileMenuOpen)} className=\"text-white\">\r\n                {mobileMenuOpen ? <FaTimes className=\"h-6 w-6\" /> : <FaBars className=\"h-6 w-6\" />}\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n                 {/* Services Dropdown */}\r\n         {servicesDropdownOpen && (\r\n           // CHANGED: Attached the panel ref here\r\n           <div \r\n             ref={dropdownPanelRef} \r\n             className=\"absolute top-full left-1/2 transform -translate-x-1/2 mt-0 w-11/12 max-w-6xl z-[10000]\"\r\n             onMouseEnter={() => setServicesDropdownOpen(true)}\r\n             onMouseLeave={() => setServicesDropdownOpen(false)}\r\n           >\r\n             {/* Invisible bridge to prevent gap */}\r\n             <div className=\"h-2 bg-transparent\"></div>\r\n             <div className=\"bg-white rounded-2xl shadow-xl py-6 border border-gray-200 fade-in-up\">\r\n               <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-6 px-8\">\r\n                 <div className=\"lg:col-span-3\">\r\n                   <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n                     {navLinks.find(link => link.dropdown)?.dropdown.map((item, index) => (\r\n                       <Link\r\n                         key={item.name}\r\n                         href={item.href}\r\n                         onClick={() => setServicesDropdownOpen(false)}\r\n                         className=\"block w-full text-left p-3 hover:bg-gray-50 transition-colors rounded-lg group\"\r\n                         style={{ animationDelay: `${index * 0.05}s` }}\r\n                       >\r\n                         <h4 className=\"font-semibold text-[var(--color-dark-blue)] mb-1 group-hover:text-[var(--color-blue)] transition-colors text-sm\">\r\n                           {item.name}\r\n                         </h4>\r\n                         <p className=\"text-xs text-gray-600 leading-relaxed\">\r\n                           {item.description}\r\n                         </p>\r\n                       </Link>\r\n                     ))}\r\n                   </div>\r\n                 </div>\r\n                 <div className=\"lg:col-span-1 lg:border-l lg:border-gray-200 lg:pl-6\">\r\n                   <div className=\"bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-lg p-4 text-white\">\r\n                     <div className=\"mb-4\">\r\n                       {/* Brochure cover image */}\r\n                       <img \r\n                         src=\"/brochure-cover.png\" \r\n                         alt=\"SecurityLit Brochure Cover\" \r\n                         className=\"w-full h-32 object-cover rounded-lg shadow-lg\"\r\n                       />\r\n                     </div>\r\n                     <h4 className=\"font-bold text-lg mb-2\">Download Brochure</h4>\r\n                     <p className=\"text-sm text-white/90 mb-4\">Get our comprehensive service guide</p>\r\n                     {/* CHANGED: Reverted to a simple and reliable <a> tag */}\r\n                     <a\r\n                       href=\"/seclit-brochure.pdf\"\r\n                       download=\"seclit-brochure.pdf\"\r\n                       onClick={() => setServicesDropdownOpen(false)}\r\n                       className=\"bg-white text-[var(--color-blue)] px-4 py-2 rounded-lg font-semibold text-sm hover:bg-gray-50 transition-colors block text-center w-full\"\r\n                     >\r\n                       Download →\r\n                     </a>\r\n                   </div>\r\n                 </div>\r\n               </div>\r\n             </div>\r\n           </div>\r\n         )}\r\n\r\n                 {/* Resources Dropdown */}\r\n         {resourcesDropdownOpen && (\r\n           <div \r\n             className=\"absolute top-full left-1/2 transform -translate-x-1/2 mt-0 w-11/12 max-w-4xl z-[10000]\"\r\n             onMouseEnter={() => setResourcesDropdownOpen(true)}\r\n             onMouseLeave={() => setResourcesDropdownOpen(false)}\r\n           >\r\n             {/* Invisible bridge to prevent gap */}\r\n             <div className=\"h-2 bg-transparent\"></div>\r\n             <div className=\"bg-white rounded-2xl shadow-xl py-6 border border-gray-200 fade-in-up\">\r\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 px-8\">\r\n              <div className=\"lg:col-span-1\">\r\n                <div className=\"grid grid-cols-1 gap-4\">\r\n                  {navLinks.find(link => link.name === \"Resources\")?.dropdown.map((item, index) => (\r\n                    <Link\r\n                      key={item.name}\r\n                      href={item.href}\r\n                      onClick={() => setResourcesDropdownOpen(false)}\r\n                      className=\"block w-full text-left p-3 hover:bg-gray-50 transition-colors rounded-lg group\"\r\n                      style={{ animationDelay: `${index * 0.1}s` }}\r\n                    >\r\n                      <h4 className=\"font-semibold text-[var(--color-dark-blue)] mb-1 group-hover:text-[var(--color-blue)] transition-colors text-sm\">\r\n                        {item.name}\r\n                      </h4>\r\n                      <p className=\"text-xs text-gray-600 leading-relaxed\">\r\n                        {item.description}\r\n                      </p>\r\n                    </Link>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n              <div className=\"lg:col-span-1 lg:border-l lg:border-gray-200 lg:pl-6\">\r\n                <div className=\"bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-lg p-4 text-white\">\r\n                  <h4 className=\"font-bold text-lg mb-2\">Stay Updated</h4>\r\n                  <p className=\"text-sm text-white/90 mb-4\">Get the latest cybersecurity insights and news</p>\r\n                  <a\r\n                    href=\"/subscribe\"\r\n                    className=\"bg-white text-[var(--color-blue)] px-4 py-2 rounded-lg font-semibold text-sm hover:bg-gray-50 transition-colors block text-center w-full\"\r\n                  >\r\n                    Subscribe →\r\n                  </a>\r\n                </div>\r\n              </div>\r\n            </div>\r\n             </div>\r\n           </div>\r\n         )}\r\n      </div>\r\n\r\n      {/* Mobile Menu */}\r\n      {mobileMenuOpen && (\r\n        <div className=\"lg:hidden absolute top-24 left-4 right-4 bg-gray-800 rounded-2xl shadow-lg z-[10000] border border-gray-700 max-h-[80vh] overflow-y-auto fade-in-up\">\r\n          <nav className=\"flex flex-col items-center space-y-2 px-4 py-6\">\r\n                        {navLinks.map((link, idx) =>\r\n              link.dropdown ? (\r\n                <div key={link.name} className=\"w-full text-center\">\r\n                   <button\r\n                    onClick={() => {\r\n                      if (openMobileDropdown === idx) {\r\n                        setOpenMobileDropdown(null);\r\n                      } else {\r\n                        setOpenMobileDropdown(idx);\r\n                      }\r\n                    }}\r\n                    className=\"w-full flex items-center justify-center gap-2 px-4 py-3 hover:text-[var(--color-dark-blue)] transition-colors font-medium text-white\"\r\n                  >\r\n                    {link.name}\r\n                    <FaChevronDown className={`h-3 w-3 transition-transform duration-300 ${openMobileDropdown === idx ? 'transform rotate-180' : ''}`} />\r\n                  </button>\r\n                  {openMobileDropdown === idx && (\r\n                    <div className=\"w-full bg-white rounded-lg py-4 mt-2 border border-gray-200 fade-in-up\">\r\n                      <div className=\"grid grid-cols-1 gap-3 px-4 mb-4 max-h-[60vh] overflow-y-auto\">\r\n                        {link.dropdown.map((item, index) => (\r\n                          <Link\r\n                            key={item.name}\r\n                            href={item.href}\r\n                            onClick={() => {\r\n                              setOpenMobileDropdown(null);\r\n                              setMobileMenuOpen(false);\r\n                            }}\r\n                            className=\"block w-full text-left p-3 hover:bg-gray-50 transition-colors rounded-lg\"\r\n                            style={{ animationDelay: `${index * 0.05}s` }}\r\n                          >\r\n                            <h4 className=\"font-semibold text-[var(--color-dark-blue)] mb-1 text-sm\">{item.name}</h4>\r\n                            <p className=\"text-xs text-gray-600\">{item.description}</p>\r\n                          </Link>\r\n                        ))}\r\n                      </div>\r\n                      <div className=\"border-t border-gray-200 pt-4 px-4\">\r\n                        <div className=\"bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-lg p-4 text-white\">\r\n                                                     <div className=\"mb-4\">\r\n                             <img \r\n                               src=\"/brochure-cover.png\"\r\n                               alt=\"SecurityLit Brochure Cover\"\r\n                               className=\"w-full h-32 object-cover rounded-lg shadow-lg\"\r\n                             />\r\n                           </div>\r\n                          <h4 className=\"font-bold text-lg mb-2\">Download Brochure</h4>\r\n                          <p className=\"text-sm text-white/90 mb-4\">Get our comprehensive service guide</p>\r\n                          {/* CHANGED: Reverted to a simple and reliable <a> tag */}\r\n                          <a\r\n                            href=\"/seclit-brochure.pdf\"\r\n                            download=\"seclit-brochure.pdf\"\r\n                            onClick={() => {\r\n                              setOpenMobileDropdown(null);\r\n                              setMobileMenuOpen(false);\r\n                            }}\r\n                            className=\"bg-white text-[var(--color-blue)] px-4 py-2 rounded-lg font-semibold text-sm hover:bg-gray-50 transition-colors block text-center w-full\"\r\n                          >\r\n                            Download →\r\n                          </a>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              ) : (\r\n                <Link key={link.name} href={link.href} onClick={() => setMobileMenuOpen(false)} className=\"block w-full text-center px-4 py-3 hover:bg-gray-700 rounded-lg font-medium text-white\">\r\n                  {link.name}\r\n                </Link>\r\n              )\r\n            )}\r\n            <div className=\"pt-4 w-full\">\r\n              <PrimaryButton className=\"w-full px-4 py-2 text-sm\" onClick={() => window.location.href='/contact'}>\r\n                Contact Us\r\n              </PrimaryButton>\r\n            </div>\r\n          </nav>\r\n        </div>\r\n      )}\r\n    </header>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,4BAA4B;;;;;AAG5B;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;QAoMF,gBA2DH;;IA9PjB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAE,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC9D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,uDAAuD;IACvD,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAClC,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEhC,sEAAsE;IACtE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,IAAI,UAAU;YACd,IAAI,kBAAkB,GAAG,+CAA+C;YAExE,MAAM;iDAAe;oBACnB,IAAI,CAAC,SAAS;wBACZ;6DAAsB;gCACpB,MAAM,iBAAiB,OAAO,OAAO;gCACrC,MAAM,mBAAmB,KAAK,GAAG,CAAC,iBAAiB;gCAEnD,eAAe;gCACf,cAAc,iBAAiB;gCAE/B,6DAA6D;gCAC7D,IAAI,mBAAmB,iBAAiB;oCACtC,iDAAiD;oCACjD,IAAI,iBAAiB,eAAe,iBAAiB,IAAI;wCACvD,+BAA+B;wCAC/B,aAAa;wCACb,QAAQ,GAAG,CAAC,kCAAkC;oCAChD,OAAO,IAAI,iBAAiB,eAAe,kBAAkB,IAAI;wCAC/D,yCAAyC;wCACzC,aAAa;wCACb,QAAQ,GAAG,CAAC,2CAA2C;oCACzD;oCAEA,eAAe;gCACjB;gCAEA,UAAU;4BACZ;;wBACA,UAAU;oBACZ;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU,cAAc;gBAAE,SAAS;YAAK;YAChE;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG;QAAC;KAAY;IAEd,0CAA0C;IAC5C,MAAM,WAAW;QACf;YACE,MAAM;YACN,UAAU;gBACR;oBAAE,MAAM;oBAAwB,aAAa;oBAA0D,MAAM;gBAAkB;gBAC/H;oBAAE,MAAM;oBAAe,aAAa;oBAA0D,MAAM;gBAAwB;gBAC5H;oBAAE,MAAM;oBAA+B,aAAa;oBAAoD,MAAM;gBAAsB;gBACpI;oBAAE,MAAM;oBAAyB,aAAa;oBAAyD,MAAM;gBAAwB;gBACrI;oBAAE,MAAM;oBAAO,aAAa;oBAA+C,MAAM;gBAAgB;gBACjG;oBAAE,MAAM;oBAAQ,aAAa;oBAAoD,MAAM;gBAAiB;gBACxG;oBAAE,MAAM;oBAAwC,aAAa;oBAA0C,MAAM;gBAAuB;gBACpI;oBAAE,MAAM;oBAA6B,aAAa;oBAAiD,MAAM;gBAAuB;gBAChI;oBAAE,MAAM;oBAAwB,aAAa;oBAA2D,MAAM;gBAAsB;gBACpI;oBAAE,MAAM;oBAAoB,aAAa;oBAA2C,MAAM;gBAA6B;gBACvH;oBAAE,MAAM;oBAA+B,aAAa;oBAAiD,MAAM;gBAA6B;gBACxI;oBAAE,MAAM;oBAAqB,aAAa;oBAA8D,MAAM;gBAA8B;gBAC5I;oBAAE,MAAM;oBAAsB,aAAa;oBAAyD,MAAM;gBAA+B;aAC1I;QACH;QACA;YAAE,MAAM;YAAqB,MAAM;QAAY;QAC/C;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YACE,MAAM;YACN,UAAU;gBACR;oBAAE,MAAM;oBAAS,aAAa;oBAAsD,MAAM;gBAAS;gBACnG;oBAAE,MAAM;oBAAQ,aAAa;oBAAoC,MAAM;gBAAQ;aAChF;QACH;QACA;YAAE,MAAM;YAAY,MAAM;QAAS;KACpC;IAED,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,SAAS,mBAAmB,KAAK;gBAC/B,IACE,mBAAmB,OAAO,IAC1B,CAAC,mBAAmB,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,KACjD,iBAAiB,OAAO,IACxB,CAAC,iBAAiB,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAC/C;oBACA,wBAAwB;oBACxB,yBAAyB;gBAC3B;YACF;YACA,SAAS,gBAAgB,CAAC,aAAa;YACvC;oCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;2BAAG,EAAE,GAAG,+DAA+D;IAEvE,oEAAoE;IAEpE,MAAM,UAAU;YAAC,EAAE,IAAI,EAAE;6BACvB,6LAAC,+JAAA,CAAA,UAAI;YAAC,MAAM,KAAK,IAAI;YAAE,WAAU;sBAC9B,KAAK,IAAI;;;;;;;IAId,qBACE,6LAAC;QAAO,WAAW,AAAC,iGAAwK,OAAxE,YAAY,8BAA8B;;0BAC5J,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAW,AAAC,gBAA4C,OAA7B,aAAa,aAAa,IAAG;kCAC3D,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDACvB,cAAA,6LAAC;4CAAI,KAAI;4CAAyB,KAAI;4CAAmB,WAAU;;;;;;;;;;;;;;;;8CAKvE,6LAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,OACb,KAAK,QAAQ,GACX,yCAAyC;sDACtB,6LAAC;4CAEjB,WAAU;4CACV,KAAK,KAAK,IAAI,KAAK,aAAa,qBAAqB;sDAErD,cAAA,6LAAC;gDACC,SAAS;oDACP,IAAI,KAAK,IAAI,KAAK,YAAY;wDAC5B,wBAAwB,CAAC;wDACzB,yBAAyB;oDAC3B,OAAO,IAAI,KAAK,IAAI,KAAK,aAAa;wDACpC,yBAAyB,CAAC;wDAC1B,wBAAwB;oDAC1B;gDACF;gDACA,cAAc;oDACZ,IAAI,KAAK,IAAI,KAAK,YAAY;wDAC5B,wBAAwB;wDACxB,yBAAyB;oDAC3B,OAAO,IAAI,KAAK,IAAI,KAAK,aAAa;wDACpC,yBAAyB;wDACzB,wBAAwB;oDAC1B;gDACF;gDACA,WAAU;;oDAEV,KAAK,IAAI;kEACV,6LAAC,iJAAA,CAAA,gBAAa;wDAAC,WAAW,AAAC,6CAAqL,OAAzI,AAAC,KAAK,IAAI,KAAK,cAAc,wBAA0B,KAAK,IAAI,KAAK,eAAe,wBAAyB,yBAAyB;;;;;;;;;;;;2CA1BzM,KAAK,IAAI;;;;iEA8BjB,6LAAC;4CAAwB,MAAM;2CAAjB,KAAK,IAAI;;;;;;;;;;8CAM7B,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,mJAAA,CAAA,gBAAa;wCAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAC;wCAAY,WAAU;kDAAsC;;;;;;;;;;;8CAMjH,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAO,SAAS,IAAM,kBAAkB,CAAC;wCAAiB,WAAU;kDAClE,+BAAiB,6LAAC,iJAAA,CAAA,UAAO;4CAAC,WAAU;;;;;iEAAe,6LAAC,iJAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAO5E,wBACC,uCAAuC;kCACvC,6LAAC;wBACC,KAAK;wBACL,WAAU;wBACV,cAAc,IAAM,wBAAwB;wBAC5C,cAAc,IAAM,wBAAwB;;0CAG5C,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;2DACZ,iBAAA,SAAS,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ,eAAnC,qCAAA,eAAsC,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,sBACzD,6LAAC,+JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,SAAS,IAAM,wBAAwB;wDACvC,WAAU;wDACV,OAAO;4DAAE,gBAAgB,AAAC,GAAe,OAAb,QAAQ,MAAK;wDAAG;;0EAE5C,6LAAC;gEAAG,WAAU;0EACX,KAAK,IAAI;;;;;;0EAEZ,6LAAC;gEAAE,WAAU;0EACV,KAAK,WAAW;;;;;;;uDAVd,KAAK,IAAI;;;;;;;;;;;;;;;sDAgBtB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAEb,cAAA,6LAAC;4DACC,KAAI;4DACJ,KAAI;4DACJ,WAAU;;;;;;;;;;;kEAGd,6LAAC;wDAAG,WAAU;kEAAyB;;;;;;kEACvC,6LAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAE1C,6LAAC;wDACC,MAAK;wDACL,UAAS;wDACT,SAAS,IAAM,wBAAwB;wDACvC,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAWZ,uCACC,6LAAC;wBACC,WAAU;wBACV,cAAc,IAAM,yBAAyB;wBAC7C,cAAc,IAAM,yBAAyB;;0CAG7C,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;0CAChB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;2DACZ,kBAAA,SAAS,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,0BAApC,sCAAA,gBAAkD,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrE,6LAAC,+JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,SAAS,IAAM,yBAAyB;wDACxC,WAAU;wDACV,OAAO;4DAAE,gBAAgB,AAAC,GAAc,OAAZ,QAAQ,KAAI;wDAAG;;0EAE3C,6LAAC;gEAAG,WAAU;0EACX,KAAK,IAAI;;;;;;0EAEZ,6LAAC;gEAAE,WAAU;0EACV,KAAK,WAAW;;;;;;;uDAVd,KAAK,IAAI;;;;;;;;;;;;;;;sDAgBtB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAyB;;;;;;kEACvC,6LAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAC1C,6LAAC;wDACC,MAAK;wDACL,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAYZ,gCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;wBACA,SAAS,GAAG,CAAC,CAAC,MAAM,MAC/B,KAAK,QAAQ,iBACX,6LAAC;gCAAoB,WAAU;;kDAC5B,6LAAC;wCACA,SAAS;4CACP,IAAI,uBAAuB,KAAK;gDAC9B,sBAAsB;4CACxB,OAAO;gDACL,sBAAsB;4CACxB;wCACF;wCACA,WAAU;;4CAET,KAAK,IAAI;0DACV,6LAAC,iJAAA,CAAA,gBAAa;gDAAC,WAAW,AAAC,6CAAqG,OAAzD,uBAAuB,MAAM,yBAAyB;;;;;;;;;;;;oCAE9H,uBAAuB,qBACtB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,sBACxB,6LAAC,+JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,SAAS;4DACP,sBAAsB;4DACtB,kBAAkB;wDACpB;wDACA,WAAU;wDACV,OAAO;4DAAE,gBAAgB,AAAC,GAAe,OAAb,QAAQ,MAAK;wDAAG;;0EAE5C,6LAAC;gEAAG,WAAU;0EAA4D,KAAK,IAAI;;;;;;0EACnF,6LAAC;gEAAE,WAAU;0EAAyB,KAAK,WAAW;;;;;;;uDAVjD,KAAK,IAAI;;;;;;;;;;0DAcpB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACc,6LAAC;4DAAI,WAAU;sEACvC,cAAA,6LAAC;gEACC,KAAI;gEACJ,KAAI;gEACJ,WAAU;;;;;;;;;;;sEAGf,6LAAC;4DAAG,WAAU;sEAAyB;;;;;;sEACvC,6LAAC;4DAAE,WAAU;sEAA6B;;;;;;sEAE1C,6LAAC;4DACC,MAAK;4DACL,UAAS;4DACT,SAAS;gEACP,sBAAsB;gEACtB,kBAAkB;4DACpB;4DACA,WAAU;sEACX;;;;;;;;;;;;;;;;;;;;;;;;+BArDD,KAAK,IAAI;;;;qDA8DnB,6LAAC,+JAAA,CAAA,UAAI;gCAAiB,MAAM,KAAK,IAAI;gCAAE,SAAS,IAAM,kBAAkB;gCAAQ,WAAU;0CACvF,KAAK,IAAI;+BADD,KAAK,IAAI;;;;;sCAKxB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,mJAAA,CAAA,gBAAa;gCAAC,WAAU;gCAA2B,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAC;0CAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlH;GAtXwB;KAAA", "debugId": null}}]}