"use client";

import React from 'react';
import { FaArrowRight } from 'react-icons/fa';
import { PrimaryButton } from '@/app/common/buttons/BrandButtons';
import { useRouter } from 'next/navigation';
import Image from 'next/image';

export default function ComplianceSection() {
  const router = useRouter();

  const handleContactRedirect = () => {
    router.push('/contact');
  };

  return (
    <section className="w-full py-12 md:py-16 lg:py-20 bg-[#F8F8F8]">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-8 md:gap-12 items-center">
          
          {/* Left Section: Compliance Logos */}
          <div className="space-y-8 md:space-y-12">
            {/* Top Row */}
            <div className="grid grid-cols-3 gap-4 md:gap-6 lg:gap-8">
              {/* HIPAA Compliance */}
              <div className="flex flex-col items-center space-y-2">
                <div className="w-20 h-20 md:w-24 md:h-24 lg:w-28 lg:h-28 rounded-lg flex items-center justify-center overflow-hidden">
                  <Image 
                    src="/images/hipaa-logo.png" 
                    alt="HIPAA Compliance Logo" 
                    width={112} 
                    height={112}
                    className="object-contain w-full h-full"
                  />
                </div>
              </div>
              
              {/* PCI DSS Compliant */}
              <div className="flex flex-col items-center space-y-2">
                <div className="w-20 h-20 md:w-24 md:h-24 lg:w-28 lg:h-28 rounded-lg flex items-center justify-center overflow-hidden">
                  <Image 
                    src="/images/pci-dss-logo.png" 
                    alt="PCI DSS Compliant Logo" 
                    width={112} 
                    height={112}
                    className="object-contain w-full h-full"
                  />
                </div>
              </div>
              
              {/* CREST */}
              <div className="flex flex-col items-center space-y-2">
                <div className="w-20 h-20 md:w-24 md:h-24 lg:w-28 lg:h-28 rounded-lg flex items-center justify-center overflow-hidden">
                  <Image 
                    src="/images/crest-logo.png" 
                    alt="CREST Logo" 
                    width={112} 
                    height={112}
                    className="object-contain w-full h-full"
                  />
                </div>
              </div>
            </div>
            
            {/* Bottom Row */}
            <div className="grid grid-cols-3 gap-4 md:gap-6 lg:gap-8">
              {/* ISO 27001 Certified */}
              <div className="flex flex-col items-center space-y-2">
                <div className="w-20 h-20 md:w-24 md:h-24 lg:w-28 lg:h-28 rounded-full flex items-center justify-center overflow-hidden">
                  <Image 
                    src="/images/iso27001-logo-2.png" 
                    alt="ISO 27001 Certified Logo" 
                    width={112} 
                    height={112}
                    className="object-contain w-full h-full"
                  />
                </div>
              </div>
              
              {/* AICPA SOC 2 */}
              <div className="flex flex-col items-center space-y-2">
                <div className="w-20 h-20 md:w-24 md:h-24 lg:w-28 lg:h-28 rounded-full flex items-center justify-center overflow-hidden">
                  <Image 
                    src="/images/aicpa-soc2-logo.jpg" 
                    alt="AICPA SOC 2 Logo" 
                    width={112} 
                    height={112}
                    className="object-contain w-full h-full"
                  />
                </div>
              </div>
              
              {/* GDPR */}
              <div className="flex flex-col items-center space-y-2">
                <div className="w-20 h-20 md:w-24 md:h-24 lg:w-28 lg:h-28 rounded-full flex items-center justify-center overflow-hidden">
                  <Image 
                    src="/images/gdpr-logo.jpg" 
                    alt="GDPR Logo" 
                    width={112} 
                    height={112}
                    className="object-contain w-full h-full"
                  />
                </div>
              </div>
            </div>
          </div>
          
          {/* Right Section: Text and Button */}
          <div className="space-y-4 md:space-y-6">
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-[var(--color-dark-blue)] leading-tight">
              Penetration Testing Requirements<br />
              Covered by <span className="text-[var(--color-blue)]">SecurityLit</span>
            </h2>
            
            <p className="text-base md:text-lg text-gray-700 max-w-lg">
              At SecurityLit, our pentesting guarantees extensive coverage of key compliance frameworks, offering robust security solutions tailored to your unique requirements.
            </p>
            
            <PrimaryButton onClick={handleContactRedirect}>
              Request a Pentest
              <FaArrowRight className="text-sm" />
            </PrimaryButton>
          </div>
        </div>
      </div>
    </section>
  );
} 