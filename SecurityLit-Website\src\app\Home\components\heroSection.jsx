// src/components/Hero.jsx (or your existing file path)
"use client";

import React, { useState } from 'react';
import { PrimaryButton, SecondaryButton, OutlinedButton } from "@/app/common/buttons/BrandButtons";
import { FaArrowRight, FaShieldAlt, FaCheckCircle, FaLock } from 'react-icons/fa';
import Image from 'next/image';

// Reusable component for the benefit list items
const BenefitItem = ({ text }) => (
  <li className="flex items-center gap-3">
    <FaCheckCircle className="text-green-500 h-5 w-5 flex-shrink-0" />
    <span className="text-white/80">{text}</span>
  </li>
);

export default function Hero() {
  const [formData, setFormData] = useState({ name: '', email: '' });
  const locations = ['New Zealand', 'India', 'Australia', 'USA'];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log("Form Submitted:", formData);
    // Add your form submission logic here
  };

  return (
    <section className="relative w-full min-h-screen flex items-center pt-16 md:pt-20 bg-white">
      {/* Background Grid Pattern */}
      <div className="absolute inset-0 cyber-grid opacity-20 z-0"></div>
      
      {/* Subtle overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/80 via-gray-50/80 to-gray-100/80 z-0"></div>
      
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-16 items-center max-w-full">
          
          {/* Left Column: Content */}
          <div className="space-y-6 md:space-y-8 w-full fade-in-up">
            <div className="inline-flex items-center gap-2 px-3 py-1.5 md:px-4 md:py-2 bg-[var(--color-blue)]/10 border border-[var(--color-blue)]/20 rounded-full text-xs md:text-sm font-semibold text-[var(--color-blue)]">
              <FaShieldAlt className="text-[var(--color-blue)] text-xs md:text-sm" />
              <span>Trusted by Fortune 500</span>
            </div>

            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-[var(--color-dark-blue)] leading-tight">
              Secure Your<br />
              <span className="text-[var(--color-blue)]">Digital Future</span>
            </h1>

            <p className="text-base md:text-lg text-gray-600 max-w-xl">
              Comprehensive cybersecurity consulting services for businesses across India. From vCISO services to red teaming, we protect what matters most.
            </p>

            <div className="flex flex-wrap items-center gap-2 md:gap-3">
              <span className="text-xs md:text-sm font-medium text-gray-500">Serving:</span>
              {locations.map((loc, index) => (
                <span 
                  key={loc} 
                  className="px-2 py-1 md:px-3 md:py-1 bg-gray-100 border border-gray-200 text-gray-700 rounded-full text-xs md:text-sm cursor-pointer"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  {loc}
                </span>
              ))}
              <span className="px-2 py-1 md:px-3 md:py-1 bg-[var(--color-blue)] text-white rounded-full text-xs md:text-sm font-semibold">
                + more
              </span>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 md:gap-4 pt-2 md:pt-4">
              <PrimaryButton className="px-4 py-2.5 md:px-5 md:py-2.5 text-sm md:text-base">
                Talk to Our Expert <FaArrowRight />
              </PrimaryButton>
              <button className="px-4 py-2.5 md:px-5 md:py-2.5 text-sm md:text-base bg-[var(--color-yellow)] text-[var(--color-gray)] border-2 border-[var(--color-yellow)] rounded-lg font-bold transition-all duration-300 hover:bg-white hover:text-[var(--color-dark-blue)] hover:border-white shadow-md flex items-center justify-center gap-2 cursor-pointer">
                Our Services
              </button>
            </div>
          </div>

          {/* Right Column: Hero Image */}
          <div className="hidden lg:block relative">
            <div className="relative w-full h-full flex items-center justify-center">
              <Image 
                src="/images/hero-image-9.svg" 
                alt="SecurityLit Cybersecurity Services" 
                width={500} 
                height={500}
                className="object-contain max-w-full max-h-full"
                priority
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
