@import "tailwindcss";

:root {
  /* Primary Blue Shades */
  --color-blue: #3366ff;
  --color-blue-secondary: #6188ff;

  /* Accent Yellow Shades */
  --color-yellow: #FBC02D;
  --color-yellow-hover: #F9A825;
  --color-yellow-bg: #FFF9C4;

  /* Dark Blue Shades */
  --color-dark-blue: #0d1a63;
  --color-dark-blue-hover: #1B263B;
  --color-dark-blue-bg: #274472;
  --color-dark-blue-subtle: #4A6FA5;

  /* Optional Neutrals */
  --color-gray: #212121;
  --color-gray-light: #E0E0E0;

  --background: #fff;
  --foreground: var(--color-dark-blue);
  --foreground-secondary: #4a5568;
}

/* Smooth scrolling for the entire page */
html {
  scroll-behavior: smooth;
}

body {
  background-color: var(--background);
  color: var(--foreground);
  font-family: var(--font-roboto), 'Roboto', sans-serif;
  margin: 0;
  padding: 0;
}

/* Typography: Poppins for headings, Roboto for paragraphs */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-poppins), 'Poppins', sans-serif;
}

p, span, div, a, li, td, th, label, input, textarea, select {
  font-family: var(--font-roboto), 'Roboto', sans-serif;
}

/* Utility classes for explicit font application */
.font-poppins {
  font-family: var(--font-poppins), 'Poppins', sans-serif !important;
}

.font-roboto {
  font-family: var(--font-roboto), 'Roboto', sans-serif !important;
}

.font-heading {
  font-family: var(--font-poppins), 'Poppins', sans-serif !important;
}

.font-body {
  font-family: var(--font-roboto), 'Roboto', sans-serif !important;
}

/* Fix for cross-browser container consistency */
.container {
  width: 100% !important;
  margin-left: auto !important;
  margin-right: auto !important;
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}

@media (min-width: 640px) {
  .container {
    padding-left: 1.5rem !important;
    padding-right: 1.5rem !important;
  }
}

@media (min-width: 1024px) {
  .container {
    padding-left: 2rem !important;
    padding-right: 2rem !important;
  }
}

/* Ensure consistent box-sizing across browsers */
*, *::before, *::after {
  box-sizing: border-box;
}

/* Fix for webkit browsers margin/padding inconsistencies */
* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

/* Ensure consistent viewport behavior across browsers */
html, body {
  width: 100%;
  overflow-x: hidden;
}

/* Fix for mobile menu positioning consistency */
.navbar-glass {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(13, 26, 99, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Ensure consistent max-width behavior */
.max-w-7xl {
  max-width: 80rem !important;
}

/* Professional Button Animations */
.btn-primary {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, var(--color-blue) 0%, var(--color-blue-secondary) 100%);
  border: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0);
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(51, 102, 255, 0.3);
}

.btn-primary:active {
  transform: translateY(0);
  transition: transform 0.1s ease;
}

.btn-secondary {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, var(--color-dark-blue) 0%, var(--color-dark-blue-hover) 100%);
  border: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0);
}

.btn-secondary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.btn-secondary:hover::before {
  left: 100%;
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(13, 26, 99, 0.3);
}

.btn-secondary:active {
  transform: translateY(0);
  transition: transform 0.1s ease;
}

.btn-outlined {
  position: relative;
  overflow: hidden;
  background: transparent;
  border: 2px solid var(--color-dark-blue);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0);
}

.btn-outlined::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background: var(--color-dark-blue);
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: -1;
}

.btn-outlined:hover::before {
  width: 100%;
}

.btn-outlined:hover {
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(13, 26, 99, 0.2);
}

.btn-outlined:active {
  transform: translateY(0);
  transition: transform 0.1s ease;
}

.btn-accent {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, var(--color-yellow) 0%, var(--color-yellow-hover) 100%);
  border: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0);
}

.btn-accent::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.btn-accent:hover::before {
  left: 100%;
}

.btn-accent:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(251, 192, 45, 0.4);
}

.btn-accent:active {
  transform: translateY(0);
  transition: transform 0.1s ease;
}

/* Glass Effect for Navbar */
.navbar-glass {
  background: rgba(75, 85, 99, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.navbar-glass.scrolled {
  background: rgba(75, 85, 99, 0.95);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Enhanced focus states for accessibility */
button:focus-visible,
a:focus-visible,
input:focus-visible,
textarea:focus-visible {
  outline: 2px solid var(--color-blue);
  outline-offset: 2px;
}

/* Loading animation for buttons */
.btn-loading {
  position: relative;
  pointer-events: none;
}

.btn-loading::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  top: 50%;
  left: 50%;
  margin-left: -8px;
  margin-top: -8px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Fade in animation for page elements */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* Blob animation for background elements */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* Hide scrollbar for webkit browsers */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

/* Custom scrollbar for TOC sections */
.custom-scrollbar {
  /* Firefox */
  scrollbar-width: thin;
  scrollbar-color: var(--color-blue) transparent;
}

/* Webkit browsers (Chrome, Safari, Edge) */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: var(--color-blue);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--color-dark-blue);
}

.custom-scrollbar::-webkit-scrollbar-thumb:active {
  background: var(--color-dark-blue);
}

@layer utilities {
  .cyber-grid {
    background-image:
      linear-gradient(to right, #3b82f6 1px, transparent 1px),
      linear-gradient(to bottom, #3b82f6 1px, transparent 1px);
    background-size: 40px 40px;
  }
  .text-brand-light-blue {
    color: var(--color-blue);
  }
  .bg-brand-light-blue {
    background-color: var(--color-blue);
  }
  .text-brand-navy {
    color: var(--color-dark-blue);
  }
}
