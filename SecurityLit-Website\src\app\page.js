import HeroSection from "@/app/Home/components/heroSection";
import OurServices from "./Home/components/OurServices";
import ComplianceSection from "./Home/components/ComplianceSection";
import WhyChooseUsSection from "./Home/components/WhyChooseUsSection";
import LogoCarouselSection from "./Home/components/LogoCarouselSection";
import Testimonial from "./Home/components/Testimonial";
import TrustedTestimonials from "./Home/components/TrustedTestimonials";
import ContactUsSection from "./Home/components/ContactUsSection";

export const metadata = {
  title: "SecurityLit | Cybersecurity Services & PTaaS Platform",
  description:
    "SecurityLit is a leading cybersecurity service provider offering vCISO as a Service, Red Teaming, VAPT, Compliance Pre-Assessment, and Cybersecurity Training. Explore our PTaaS platform, Capture the Bug, for continuous pentesting and vulnerability management.",
  openGraph: {
    title: " Cybersecurity Services & PTaaS Platform | SecurityLit ",
    description:
      "SecurityLit is a leading cybersecurity service provider offering vCISO as a Service, Red Teaming, VAPT, Compliance Pre-Assessment, and Cybersecurity Training. Explore our PTaaS platform, Capture the Bug, for continuous pentesting and vulnerability management.",
    url: "https://securitylit.com/",
    siteName: "SecurityLit",
    images: [
      {
        url: "/your-hero-bg.jpg",
        width: 1200,
        height: 630,
        alt: "SecurityLit Cybersecurity Services",
      },
    ],
    locale: "en_GB",
    type: "website",
  },
};

export default function Home() {
  return (
    <main className="min-h-screen w-full">
      <HeroSection />
      <OurServices/>
      {/* <LogoCarouselSection /> */}
      <WhyChooseUsSection />
      <ComplianceSection />
      <Testimonial />
      {/* <TrustedTestimonials /> */}
      <ContactUsSection />

    </main>
  );
}
