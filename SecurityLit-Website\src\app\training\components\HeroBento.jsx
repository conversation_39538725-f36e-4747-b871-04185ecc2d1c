"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { Shield, Play, ArrowRight, GraduationCap, CheckCircle, Users, Star, Clock, Award } from 'lucide-react';
import BreadcrumbNavigation from '../../common/components/BreadcrumbNavigation';

export default function HeroBento() {
  const breadcrumbItems = [
    {
      name: "Home",
      url: "/",
      iconKey: "home",
      description: "Return to homepage"
    },
    {
      name: "Training",
      url: "/training",
      current: true,
      iconKey: "graduation-cap",
      description: "Explore SecurityLit's cybersecurity training programs"
    }
  ];

  const keyBenefits = [
    "Complete pentesting skills with real-time live projects",
    "Latest tools and topics in cybersecurity",
    "Free and premium pathways available"
  ];

  const trustStats = [
    { value: "500+", label: "Students Trained", icon: Users },
    { value: "98%", label: "Success Rate", icon: Star },
    { value: "83", label: "Total Lessons", icon: Clock },
    { value: "11", label: "Course Sections", icon: Award }
  ];

  return (
    <div className="min-h-screen bg-white">
      <div className="flex flex-col lg:flex-row min-h-screen relative">

        {/* Left Section - Optimized Content with Rounded Corner */}
        <div className="w-full lg:w-1/2 bg-gradient-to-br from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)] relative overflow-hidden lg:rounded-br-[100px]">
          {/* Background Pattern */}
          <div className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-10"
               style={{ backgroundImage: 'url(/images/hexagon.svg)' }}>
          </div>
          <div className="absolute inset-0 bg-[var(--color-dark-blue)]/90 lg:rounded-br-[100px]"></div>
          
          <div className="relative z-10 flex justify-center px-4 sm:px-6 lg:px-12 py-8 sm:py-12 lg:pt-20 lg:pb-8 min-h-screen lg:h-full">
            <div className="max-w-lg w-full flex flex-col justify-center">
              {/* Breadcrumb */}
              <div className="mb-4 mt-2 lg:mt-0">
                <BreadcrumbNavigation items={breadcrumbItems} className="text-white" />
              </div>

              {/* Main Heading - Enhanced */}
              <motion.h1
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 leading-tight"
              >
                Elite Security
                <span className="block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent">
                  Training
                </span>
              </motion.h1>
              
              {/* Streamlined Subtitle */}
              <motion.h2
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="text-lg sm:text-xl lg:text-2xl font-bold text-white mb-3 leading-tight"
              >
                Launch Your Cyber Security Career
              </motion.h2>

              <motion.h3
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                className="text-base sm:text-lg text-[var(--color-yellow)] mb-4 font-semibold"
              >
                Free and Premium Pathways
              </motion.h3>
              
              {/* Compact Description - 2 lines max */}
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="text-sm sm:text-base text-white/90 mb-6 leading-relaxed"
              >
                Dive into the world of penetration testing with our refined program.
                Designed for aspiring security professionals with complete pentesting skills and real-time live projects.
              </motion.p>

              {/* Streamlined Key Benefits - 3 items only */}
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.5 }}
                className="space-y-3 mb-8"
              >
                {keyBenefits.map((benefit, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div className="w-5 h-5 bg-[var(--color-yellow)] rounded-full flex items-center justify-center flex-shrink-0">
                      <CheckCircle className="w-3 h-3 text-[var(--color-dark-blue)]" />
                    </div>
                    <span className="text-white/90 text-sm sm:text-base">{benefit}</span>
                  </div>
                ))}
              </motion.div>

              {/* Enhanced CTA Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                className="flex flex-col gap-3"
              >
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="group bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-white px-4 sm:px-6 py-2.5 sm:py-3 rounded-lg font-semibold text-sm sm:text-base transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center gap-2"
                >
                  <Play className="w-4 h-4" />
                  Start Free Training
                  <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                </motion.button>

                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="group bg-white/10 backdrop-blur-sm text-white px-4 sm:px-6 py-2.5 sm:py-3 rounded-lg font-semibold text-sm sm:text-base transition-all duration-300 border-2 border-white/20 hover:bg-white/20 flex items-center justify-center gap-2"
                >
                  <GraduationCap className="w-4 h-4" />
                  Upgrade to Premium
                </motion.button>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Cyber Security Icon Separator - Hidden on mobile */}
        <div className="hidden lg:block absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 z-20 pointer-events-none">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1, delay: 0.5 }}
            className="relative"
          >
            <div className="w-24 h-24 lg:w-32 lg:h-32 bg-white rounded-full shadow-2xl flex items-center justify-center p-4">
              <img 
                src="/cyber-security.png" 
                alt="Cyber Security" 
                className="w-full h-full object-contain"
              />
            </div>
            {/* Subtle glow effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-[var(--color-blue)]/20 to-[var(--color-blue-secondary)]/20 rounded-full blur-xl -z-10"></div>
          </motion.div>
        </div>

        {/* Right Section - Enhanced Visual Focus */}
        <div className="w-full lg:w-1/2 bg-white">
          <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-12 py-8 sm:py-12 lg:pt-20 lg:pb-8 min-h-screen lg:h-full flex flex-col justify-center">
            
            {/* Enhanced Hero Visual */}
            <motion.div 
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 1, delay: 0.3 }}
              className="relative mb-6"
            >
              <div className="relative bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-2xl p-4 sm:p-6 lg:p-8 border-2 border-[var(--color-blue)]/20">
                <div className="absolute inset-0 bg-gradient-to-br from-[var(--color-blue)]/5 to-[var(--color-blue-secondary)]/5 rounded-2xl"></div>
                
                <div className="relative z-10 text-center">
                  <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                    <img
                      src="/SecurityLit_Icon_White.png"
                      alt="SecurityLit Logo"
                      className="w-8 h-8 sm:w-12 sm:h-12 object-contain"
                    />
                  </div>

                  <h3 className="text-lg sm:text-xl font-bold text-[var(--color-dark-blue)] mb-2">
                    SecurityLit Presents
                  </h3>
                  <p className="text-[var(--foreground-secondary)] text-sm sm:text-base">
                    Professional cybersecurity training designed by industry experts
                  </p>
                </div>
              </div>
            </motion.div>

            {/* Enhanced Trust Indicators */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="grid grid-cols-2 gap-3 sm:gap-4 mb-6"
            >
              {trustStats.map((stat, index) => (
                <motion.div 
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7 + index * 0.1, duration: 0.6 }}
                  className="text-center group"
                >
                  <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-lg flex items-center justify-center mx-auto mb-2 group-hover:scale-110 transition-transform">
                    <stat.icon className="w-4 h-4 sm:w-5 sm:h-5 text-[var(--color-blue)]" />
                  </div>
                  <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-[var(--color-dark-blue)] mb-1">
                    {stat.value}
                  </div>
                  <div className="text-[var(--foreground-secondary)] text-xs font-medium">
                    {stat.label}
                  </div>
                </motion.div>
              ))}
            </motion.div>

            
          </div>
        </div>
      </div>
    </div>
  );
} 