/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        'poppins': ['var(--font-poppins)', 'Poppins', 'sans-serif'],
        'roboto': ['var(--font-roboto)', 'Roboto', 'sans-serif'],
        'sans': ['var(--font-roboto)', 'Roboto', 'sans-serif'], // Default sans font
        'heading': ['var(--font-poppins)', 'Poppins', 'sans-serif'], // Custom heading font
      },
      colors: {
        // Primary Blue Shades
        'color-blue': '#3366ff',
        'color-blue-secondary': '#6188ff',
        
        // Accent Yellow Shades
        'color-yellow': '#FBC02D',
        'color-yellow-hover': '#F9A825',
        'color-yellow-bg': '#FFF9C4',
        
        // Dark Blue Shades
        'color-dark-blue': '#0d1a63',
        'color-dark-blue-hover': '#1B263B',
        'color-dark-blue-bg': '#274472',
        'color-dark-blue-subtle': '#4A6FA5',
        
        // Optional Neutrals
        'color-gray': '#212121',
        'color-gray-light': '#E0E0E0',
      },
    },
  },
  plugins: [],
}
